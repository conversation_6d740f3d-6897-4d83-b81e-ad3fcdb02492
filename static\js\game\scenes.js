/**
 * Game Scene Implementations for Bomberman
 */

// Wait for BombermanGame to be defined, then extend it
(function() {
    function extendBombermanGame() {
        if (typeof BombermanGame === 'undefined') {
            setTimeout(extendBombermanGame, 100);
            return;
        }

        // Extend the BombermanGame class with scene implementations
        Object.assign(BombermanGame.prototype, {
    
    // Menu Scene Implementation
    enterMenuScene() {
        this.currentState = 'MENU';
        this.clearGameContainer();
        
        // Create menu UI
        this.menuContainer = Utils.DOM.createElement('div', 'menu-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Logo
        const logo = Utils.DOM.createElement('img', 'game-logo', {
            width: '60vw',
            maxWidth: '600px',
            marginBottom: '50px'
        });
        logo.src = './static/images/logo.png';
        logo.alt = 'Bomberman Logo';
        
        // Menu buttons
        const menuButtons = Utils.DOM.createElement('div', 'menu-buttons', {
            display: 'flex',
            flexDirection: 'column',
            gap: '20px'
        });
        
        const startButton = this.createMenuButton('Start Game', () => {
            this.framework.switchScene('nickname');
        });
        
        const settingsButton = this.createMenuButton('Settings', () => {
            // TODO: Implement settings
            console.log('Settings not implemented yet');
        });
        
        const exitButton = this.createMenuButton('Exit', () => {
            window.close();
        });
        
        menuButtons.appendChild(startButton);
        menuButtons.appendChild(settingsButton);
        menuButtons.appendChild(exitButton);
        
        this.menuContainer.appendChild(logo);
        this.menuContainer.appendChild(menuButtons);
        this.gameContainer.appendChild(this.menuContainer);
    },
    
    exitMenuScene() {
        if (this.menuContainer) {
            this.gameContainer.removeChild(this.menuContainer);
            this.menuContainer = null;
        }
    },
    
    updateMenuScene(deltaTime) {
        // Handle menu input
        if (this.inputSystem.isKeyPressed('Enter')) {
            this.framework.switchScene('nickname');
        }
    },
    
    renderMenuScene() {
        // Menu rendering is handled by DOM
    },
    
    // Nickname Entry Scene Implementation
    enterNicknameScene() {
        this.currentState = 'NICKNAME';
        this.clearGameContainer();
        
        this.nicknameContainer = Utils.DOM.createElement('div', 'nickname-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Title
        const title = Utils.DOM.createElement('h1', 'nickname-title', {
            fontSize: '2.5rem',
            marginBottom: '40px',
            textTransform: 'uppercase',
            letterSpacing: '5px'
        });
        title.textContent = 'Enter Your Nickname';
        
        // Input form
        const form = Utils.DOM.createElement('form', 'nickname-form', {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '20px'
        });
        
        const input = Utils.DOM.createElement('input', 'nickname-input', {
            padding: '15px 20px',
            fontSize: '1.2rem',
            border: '2px solid #34495e',
            borderRadius: '5px',
            backgroundColor: '#2c3e50',
            color: 'white',
            textAlign: 'center',
            minWidth: '300px'
        });
        input.type = 'text';
        input.placeholder = 'Enter nickname...';
        input.maxLength = 20;
        input.required = true;
        
        const submitButton = this.createMenuButton('Join Game', () => {
            this.handleNicknameSubmit(input.value.trim());
        });
        
        const backButton = this.createMenuButton('Back to Main Menu', () => {
            window.location.href = '/';
        }, '#e74c3c');
        
        form.appendChild(input);
        form.appendChild(submitButton);
        form.appendChild(backButton);
        
        // Handle form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleNicknameSubmit(input.value.trim());
        });
        
        this.nicknameContainer.appendChild(title);
        this.nicknameContainer.appendChild(form);
        this.gameContainer.appendChild(this.nicknameContainer);
        
        // Focus input
        setTimeout(() => input.focus(), 100);
    },
    
    exitNicknameScene() {
        if (this.nicknameContainer) {
            this.gameContainer.removeChild(this.nicknameContainer);
            this.nicknameContainer = null;
        }
    },
    
    updateNicknameScene(deltaTime) {
        // Nickname scene update logic
    },
    
    renderNicknameScene() {
        // Nickname rendering is handled by DOM
    },
    
    handleNicknameSubmit(nickname) {
        if (nickname.length < 2) {
            alert('Nickname must be at least 2 characters long');
            return;
        }
        
        // Initialize multiplayer manager
        if (!this.multiplayerManager) {
            this.multiplayerManager = new MultiplayerManager(this);
        }
        
        this.playerNickname = nickname;
        this.multiplayerManager.setNickname(nickname);
        
        // Try to connect to server
        this.multiplayerManager.connect();
        
        // Switch to lobby scene
        this.framework.switchScene('lobby');
    },
    
    // Lobby Scene Implementation
    enterLobbyScene() {
        this.currentState = 'LOBBY';
        this.clearGameContainer();
        
        this.lobbyContainer = Utils.DOM.createElement('div', 'lobby-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif',
            padding: '20px',
            boxSizing: 'border-box'
        });
        
        // Create lobby UI
        this.createLobbyUI();
        
        this.gameContainer.appendChild(this.lobbyContainer);
        
        // Setup lobby timers
        this.lobbyTimer = 20; // 20 seconds to wait for players
        this.countdownTimer = 0;
        this.isCountingDown = false;
        
        // Start lobby timer
        this.lobbyTimerInterval = setInterval(() => {
            this.updateLobbyTimer();
        }, 1000);
    },
    
    exitLobbyScene() {
        if (this.lobbyContainer) {
            this.gameContainer.removeChild(this.lobbyContainer);
            this.lobbyContainer = null;
        }
        
        if (this.lobbyTimerInterval) {
            clearInterval(this.lobbyTimerInterval);
            this.lobbyTimerInterval = null;
        }
    },
    
    updateLobbyScene(deltaTime) {
        // Update chat
        this.updateLobbyChat();
    },
    
    renderLobbyScene() {
        // Lobby rendering is handled by DOM
    },
    
    createLobbyUI() {
        // Header
        const header = Utils.DOM.createElement('div', 'lobby-header', {
            textAlign: 'center',
            marginBottom: '30px'
        });
        
        const title = Utils.DOM.createElement('h1', '', {
            fontSize: '2rem',
            marginBottom: '10px'
        });
        title.textContent = 'Waiting for Players';
        
        this.playerCountDisplay = Utils.DOM.createElement('div', '', {
            fontSize: '1.2rem',
            color: '#3498db'
        });
        this.updatePlayerCountDisplay();
        
        this.timerDisplay = Utils.DOM.createElement('div', '', {
            fontSize: '1.5rem',
            color: '#e74c3c',
            marginTop: '10px'
        });
        
        header.appendChild(title);
        header.appendChild(this.playerCountDisplay);
        header.appendChild(this.timerDisplay);
        
        // Main content area
        const content = Utils.DOM.createElement('div', 'lobby-content', {
            display: 'flex',
            height: 'calc(100% - 150px)',
            gap: '20px'
        });
        
        // Players list
        const playersSection = Utils.DOM.createElement('div', 'players-section', {
            flex: '1',
            backgroundColor: '#2c3e50',
            borderRadius: '10px',
            padding: '20px'
        });
        
        const playersTitle = Utils.DOM.createElement('h3', '', {
            marginBottom: '15px',
            textAlign: 'center'
        });
        playersTitle.textContent = 'Players';
        
        this.playersList = Utils.DOM.createElement('div', 'players-list', {
            display: 'flex',
            flexDirection: 'column',
            gap: '10px'
        });
        
        playersSection.appendChild(playersTitle);
        playersSection.appendChild(this.playersList);
        
        // Chat section
        const chatSection = Utils.DOM.createElement('div', 'chat-section', {
            flex: '1',
            backgroundColor: '#2c3e50',
            borderRadius: '10px',
            padding: '20px',
            display: 'flex',
            flexDirection: 'column'
        });
        
        const chatTitle = Utils.DOM.createElement('h3', '', {
            marginBottom: '15px',
            textAlign: 'center'
        });
        chatTitle.textContent = 'Chat';
        
        this.chatMessages = Utils.DOM.createElement('div', 'chat-messages', {
            flex: '1',
            backgroundColor: '#34495e',
            borderRadius: '5px',
            padding: '10px',
            overflowY: 'auto',
            marginBottom: '10px',
            minHeight: '200px'
        });
        
        const chatInputContainer = Utils.DOM.createElement('div', 'chat-input-container', {
            display: 'flex',
            gap: '10px'
        });
        
        this.chatInput = Utils.DOM.createElement('input', 'chat-input', {
            flex: '1',
            padding: '10px',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: '#34495e',
            color: 'white'
        });
        this.chatInput.type = 'text';
        this.chatInput.placeholder = 'Type a message...';
        this.chatInput.maxLength = 200;
        
        const sendButton = Utils.DOM.createElement('button', 'send-button', {
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: '#3498db',
            color: 'white',
            cursor: 'pointer'
        });
        sendButton.textContent = 'Send';
        
        // Chat event handlers
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });
        
        sendButton.addEventListener('click', () => {
            this.sendChatMessage();
        });
        
        chatInputContainer.appendChild(this.chatInput);
        chatInputContainer.appendChild(sendButton);
        
        chatSection.appendChild(chatTitle);
        chatSection.appendChild(this.chatMessages);
        chatSection.appendChild(chatInputContainer);
        
        content.appendChild(playersSection);
        content.appendChild(chatSection);
        
        this.lobbyContainer.appendChild(header);
        this.lobbyContainer.appendChild(content);
        
        // Update players list
        this.updatePlayersList();
    },
    
    updatePlayerCountDisplay() {
        if (this.playerCountDisplay) {
            const count = this.multiplayerManager ? this.multiplayerManager.getPlayerCount() : 1;
            this.playerCountDisplay.textContent = `Players: ${count}/4`;
        }
    },
    
    updatePlayersList() {
        if (!this.playersList) return;
        
        this.playersList.innerHTML = '';
        
        if (this.multiplayerManager) {
            const players = this.multiplayerManager.getPlayers();
            players.forEach((player, index) => {
                const playerElement = Utils.DOM.createElement('div', 'player-item', {
                    padding: '10px',
                    backgroundColor: '#34495e',
                    borderRadius: '5px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px'
                });
                
                const playerColor = Utils.DOM.createElement('div', 'player-color', {
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    backgroundColor: this.getPlayerColor(index)
                });
                
                const playerName = Utils.DOM.createElement('span', 'player-name');
                playerName.textContent = player.nickname || `Player ${index + 1}`;
                
                playerElement.appendChild(playerColor);
                playerElement.appendChild(playerName);
                this.playersList.appendChild(playerElement);
            });
        } else {
            // Local player only
            const playerElement = Utils.DOM.createElement('div', 'player-item', {
                padding: '10px',
                backgroundColor: '#34495e',
                borderRadius: '5px'
            });
            playerElement.textContent = this.playerNickname || 'Player 1';
            this.playersList.appendChild(playerElement);
        }
    },
    
    updateLobbyTimer() {
        const playerCount = this.multiplayerManager ? this.multiplayerManager.getPlayerCount() : 1;
        
        if (!this.isCountingDown) {
            if (playerCount >= 4) {
                // Start countdown immediately with 4 players
                this.startCountdown();
            } else if (playerCount >= 2) {
                this.lobbyTimer--;
                if (this.lobbyTimer <= 0) {
                    this.startCountdown();
                } else {
                    this.timerDisplay.textContent = `Game starts in ${this.lobbyTimer}s (waiting for more players)`;
                }
            } else {
                this.timerDisplay.textContent = 'Waiting for at least 2 players...';
            }
        } else {
            this.countdownTimer--;
            if (this.countdownTimer <= 0) {
                this.startGame();
            } else {
                this.timerDisplay.textContent = `Game starting in ${this.countdownTimer}s!`;
            }
        }
    },
    
    startCountdown() {
        this.isCountingDown = true;
        this.countdownTimer = 10;
        this.timerDisplay.textContent = `Game starting in ${this.countdownTimer}s!`;
    },
    
    startGame() {
        this.framework.switchScene('game');
    },
    
    sendChatMessage() {
        const message = this.chatInput.value.trim();
        if (message) {
            if (this.multiplayerManager) {
                this.multiplayerManager.sendChatMessage(message);
            } else {
                // Local chat message
                this.addChatMessage({
                    nickname: this.playerNickname,
                    message: message,
                    timestamp: Date.now()
                });
            }
            this.chatInput.value = '';
        }
    },
    
    addChatMessage(data) {
        if (!this.chatMessages) return;
        
        const messageElement = Utils.DOM.createElement('div', 'chat-message', {
            marginBottom: '5px',
            padding: '5px',
            borderRadius: '3px',
            backgroundColor: '#2c3e50'
        });
        
        const time = new Date(data.timestamp).toLocaleTimeString();
        messageElement.innerHTML = `<strong>${data.nickname}</strong> [${time}]: ${data.message}`;
        
        this.chatMessages.appendChild(messageElement);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    },
    
    updateLobbyChat() {
        if (this.multiplayerManager) {
            const messages = this.multiplayerManager.getChatMessages();
            // Update chat display if needed
        }
    },
    
    // Utility methods
    createMenuButton(text, onClick, backgroundColor = '#3498db') {
        const button = Utils.DOM.createElement('button', 'menu-button', {
            padding: '15px 30px',
            fontSize: '1.1rem',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: backgroundColor,
            color: 'white',
            cursor: 'pointer',
            textTransform: 'uppercase',
            letterSpacing: '2px',
            fontFamily: 'EngraversGothic, sans-serif',
            transition: 'all 0.3s ease'
        });
        
        button.textContent = text;
        button.addEventListener('click', onClick);
        
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = 'scale(1)';
            button.style.boxShadow = 'none';
        });
        
        return button;
    },
    
    getPlayerColor(index) {
        const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
        return colors[index % colors.length];
    }
        }); // End of Object.assign
    } // End of extendBombermanGame function

    // Start the extension process
    extendBombermanGame();
})(); // End of IIFE
