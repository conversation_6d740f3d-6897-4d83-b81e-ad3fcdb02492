/**
 * Game Scene Implementations for Bomberman
 */

// Wait for BombermanGame to be defined, then extend it
(function() {
    function extendBombermanGame() {
        if (typeof BombermanGame === 'undefined') {
            setTimeout(extendBombermanGame, 100);
            return;
        }

        // Extend the BombermanGame class with scene implementations
        Object.assign(BombermanGame.prototype, {
    
    // Menu Scene Implementation
    enterMenuScene() {
        this.currentState = 'MENU';
        this.clearGameContainer();
        
        // Create menu UI
        this.menuContainer = Utils.DOM.createElement('div', 'menu-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Logo
        const logo = Utils.DOM.createElement('img', 'game-logo', {
            width: '60vw',
            maxWidth: '600px',
            marginBottom: '50px'
        });
        logo.src = './static/images/logo.png';
        logo.alt = 'Bomberman Logo';
        
        // Menu buttons
        const menuButtons = Utils.DOM.createElement('div', 'menu-buttons', {
            display: 'flex',
            flexDirection: 'column',
            gap: '20px'
        });
        
        const startButton = this.createMenuButton('Start Game', () => {
            this.framework.switchScene('levelSelect');
        });
        
        const settingsButton = this.createMenuButton('Settings', () => {
            // TODO: Implement settings
            console.log('Settings not implemented yet');
        });
        
        const exitButton = this.createMenuButton('Exit', () => {
            window.close();
        });
        
        menuButtons.appendChild(startButton);
        menuButtons.appendChild(settingsButton);
        menuButtons.appendChild(exitButton);
        
        this.menuContainer.appendChild(logo);
        this.menuContainer.appendChild(menuButtons);
        this.gameContainer.appendChild(this.menuContainer);
    },
    
    exitMenuScene() {
        if (this.menuContainer) {
            this.gameContainer.removeChild(this.menuContainer);
            this.menuContainer = null;
        }
    },
    
    updateMenuScene(deltaTime) {
        // Handle menu input
        if (this.inputSystem.isKeyPressed('Enter')) {
            this.framework.switchScene('nickname');
        }
    },
    
    renderMenuScene() {
        // Menu rendering is handled by DOM
    },
    
    // Nickname Entry Scene Implementation
    enterNicknameScene() {
        this.currentState = 'NICKNAME';
        this.clearGameContainer();
        
        this.nicknameContainer = Utils.DOM.createElement('div', 'nickname-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Title
        const title = Utils.DOM.createElement('h1', 'nickname-title', {
            fontSize: '2.5rem',
            marginBottom: '40px',
            textTransform: 'uppercase',
            letterSpacing: '5px'
        });
        title.textContent = 'Enter Your Nickname';
        
        // Input form
        const form = Utils.DOM.createElement('form', 'nickname-form', {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '20px'
        });
        
        const input = Utils.DOM.createElement('input', 'nickname-input', {
            padding: '15px 20px',
            fontSize: '1.2rem',
            border: '2px solid #34495e',
            borderRadius: '5px',
            backgroundColor: '#2c3e50',
            color: 'white',
            textAlign: 'center',
            minWidth: '300px'
        });
        input.type = 'text';
        input.placeholder = 'Enter nickname...';
        input.maxLength = 20;
        input.required = true;
        
        const submitButton = this.createMenuButton('Join Game', () => {
            this.handleNicknameSubmit(input.value.trim());
        });
        
        const backButton = this.createMenuButton('Back to Main Menu', () => {
            window.location.href = '/';
        }, '#e74c3c');
        
        form.appendChild(input);
        form.appendChild(submitButton);
        form.appendChild(backButton);
        
        // Handle form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleNicknameSubmit(input.value.trim());
        });
        
        this.nicknameContainer.appendChild(title);
        this.nicknameContainer.appendChild(form);
        this.gameContainer.appendChild(this.nicknameContainer);
        
        // Focus input
        setTimeout(() => input.focus(), 100);
    },
    
    exitNicknameScene() {
        if (this.nicknameContainer) {
            this.gameContainer.removeChild(this.nicknameContainer);
            this.nicknameContainer = null;
        }
    },
    
    updateNicknameScene(deltaTime) {
        // Nickname scene update logic
    },
    
    renderNicknameScene() {
        // Nickname rendering is handled by DOM
    },
    
    handleNicknameSubmit(nickname) {
        if (nickname.length < 2) {
            alert('Nickname must be at least 2 characters long');
            return;
        }
        
        // Initialize multiplayer manager
        if (!this.multiplayerManager) {
            this.multiplayerManager = new MultiplayerManager(this);
        }
        
        this.playerNickname = nickname;
        this.multiplayerManager.setNickname(nickname);
        
        // Try to connect to server
        this.multiplayerManager.connect();
        
        // Switch to lobby scene
        this.framework.switchScene('lobby');
    },

    // Level Selection Scene Implementation
    enterLevelSelectScene() {
        this.currentState = 'LEVEL_SELECT';
        this.clearGameContainer();

        // Initialize levels if not already done
        if (!this.gameLevels) {
            this.gameLevels = new GameLevels();
        }

        this.levelSelectContainer = Utils.DOM.createElement('div', 'level-select-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif',
            padding: '20px',
            boxSizing: 'border-box',
            overflow: 'auto'
        });

        // Title
        const title = Utils.DOM.createElement('h1', 'level-select-title', {
            textAlign: 'center',
            fontSize: '3rem',
            marginBottom: '30px',
            color: '#3498db',
            textTransform: 'uppercase',
            letterSpacing: '3px'
        });
        title.textContent = 'Select Level';

        // Subtitle
        const subtitle = Utils.DOM.createElement('p', 'level-select-subtitle', {
            textAlign: 'center',
            fontSize: '1.2rem',
            marginBottom: '40px',
            color: '#bdc3c7'
        });
        subtitle.textContent = 'Choose your battlefield and prepare for battle!';

        // Levels grid
        const levelsGrid = Utils.DOM.createElement('div', 'levels-grid', {
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '20px',
            maxWidth: '1200px',
            margin: '0 auto',
            marginBottom: '40px'
        });

        // Create level cards
        const levels = this.gameLevels.getAllLevels();
        levels.forEach(level => {
            const levelCard = this.createLevelCard(level);
            levelsGrid.appendChild(levelCard);
        });

        // Back button
        const backButton = this.createMenuButton('← Back to Menu', () => {
            this.framework.switchScene('menu');
        }, '#e74c3c');

        backButton.style.margin = '20px auto';
        backButton.style.display = 'block';

        this.levelSelectContainer.appendChild(title);
        this.levelSelectContainer.appendChild(subtitle);
        this.levelSelectContainer.appendChild(levelsGrid);
        this.levelSelectContainer.appendChild(backButton);
        this.gameContainer.appendChild(this.levelSelectContainer);
    },

    exitLevelSelectScene() {
        if (this.levelSelectContainer) {
            this.gameContainer.removeChild(this.levelSelectContainer);
            this.levelSelectContainer = null;
        }
    },

    updateLevelSelectScene(deltaTime) {
        // Handle level selection input
        if (this.inputSystem.isKeyPressed('Escape')) {
            this.framework.switchScene('menu');
        }
    },

    renderLevelSelectScene() {
        // Level selection rendering is handled by DOM
    },

    createLevelCard(level) {
        const card = Utils.DOM.createElement('div', 'level-card', {
            backgroundColor: '#34495e',
            border: '2px solid #2c3e50',
            borderRadius: '10px',
            padding: '20px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden'
        });

        // Hover effects
        card.addEventListener('mouseenter', () => {
            card.style.borderColor = '#3498db';
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 20px rgba(52, 152, 219, 0.3)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.borderColor = '#2c3e50';
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'none';
        });

        // Level name
        const name = Utils.DOM.createElement('h3', 'level-name', {
            fontSize: '1.5rem',
            marginBottom: '10px',
            color: '#3498db',
            textTransform: 'uppercase',
            letterSpacing: '2px'
        });
        name.textContent = level.name;

        // Difficulty badge
        const difficulty = Utils.DOM.createElement('span', 'level-difficulty', {
            position: 'absolute',
            top: '10px',
            right: '10px',
            backgroundColor: this.getDifficultyColor(level.difficulty),
            color: 'white',
            padding: '5px 10px',
            borderRadius: '15px',
            fontSize: '0.8rem',
            fontWeight: 'bold'
        });
        difficulty.textContent = level.difficulty;

        // Description
        const description = Utils.DOM.createElement('p', 'level-description', {
            fontSize: '1rem',
            marginBottom: '15px',
            color: '#bdc3c7',
            lineHeight: '1.4'
        });
        description.textContent = level.description;

        // Preview
        const preview = this.createLevelPreview(level);

        // Features
        const features = Utils.DOM.createElement('div', 'level-features', {
            marginTop: '15px'
        });

        if (level.specialFeatures.length > 0) {
            const featuresTitle = Utils.DOM.createElement('h4', 'features-title', {
                fontSize: '0.9rem',
                marginBottom: '5px',
                color: '#f39c12'
            });
            featuresTitle.textContent = 'Special Features:';

            const featuresList = Utils.DOM.createElement('ul', 'features-list', {
                listStyle: 'none',
                padding: '0',
                margin: '0'
            });

            level.specialFeatures.forEach(feature => {
                const featureItem = Utils.DOM.createElement('li', 'feature-item', {
                    fontSize: '0.8rem',
                    color: '#95a5a6',
                    marginBottom: '2px'
                });
                featureItem.textContent = `• ${feature.replace('_', ' ')}`;
                featuresList.appendChild(featureItem);
            });

            features.appendChild(featuresTitle);
            features.appendChild(featuresList);
        }

        // Click handler
        card.addEventListener('click', () => {
            this.selectLevel(level);
        });

        card.appendChild(difficulty);
        card.appendChild(name);
        card.appendChild(description);
        card.appendChild(preview);
        card.appendChild(features);

        return card;
    },

    createLevelPreview(level) {
        const preview = Utils.DOM.createElement('div', 'level-preview', {
            backgroundColor: '#2c3e50',
            border: '1px solid #34495e',
            borderRadius: '5px',
            padding: '10px',
            fontFamily: 'monospace',
            fontSize: '8px',
            lineHeight: '8px',
            textAlign: 'center',
            margin: '10px 0',
            overflow: 'hidden'
        });

        const previewGrid = level.preview;
        let previewText = '';

        previewGrid.forEach(row => {
            previewText += row.join('') + '\n';
        });

        preview.textContent = previewText;

        return preview;
    },

    getDifficultyColor(difficulty) {
        switch (difficulty.toLowerCase()) {
            case 'easy': return '#27ae60';
            case 'medium': return '#f39c12';
            case 'hard': return '#e74c3c';
            case 'expert': return '#8e44ad';
            case 'variable': return '#3498db';
            default: return '#95a5a6';
        }
    },

    selectLevel(level) {
        console.log('Selected level:', level.name);

        // Store selected level in game
        this.selectedLevel = level;

        // Apply level configuration to game config
        this.gameConfig.mapWidth = level.mapWidth;
        this.gameConfig.mapHeight = level.mapHeight;
        this.gameConfig.powerUpChance = level.powerUpChance;

        // Switch to nickname scene
        this.framework.switchScene('nickname');
    },

    // Lobby Scene Implementation
    enterLobbyScene() {
        this.currentState = 'LOBBY';
        this.clearGameContainer();

        this.lobbyContainer = Utils.DOM.createElement('div', 'lobby-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif',
            padding: '20px',
            boxSizing: 'border-box'
        });

        // Create lobby UI
        this.createLobbyUI();

        // Add selected level info
        this.addSelectedLevelInfo();

        this.gameContainer.appendChild(this.lobbyContainer);

        // Setup lobby timers
        this.lobbyTimer = 20; // 20 seconds to wait for players
        this.countdownTimer = 0;
        this.isCountingDown = false;

        // Start lobby timer
        this.lobbyTimerInterval = setInterval(() => {
            this.updateLobbyTimer();
        }, 1000);

        // Setup chat event listeners
        this.setupChatEventListeners();
    },
    
    exitLobbyScene() {
        if (this.lobbyContainer) {
            this.gameContainer.removeChild(this.lobbyContainer);
            this.lobbyContainer = null;
        }
        
        if (this.lobbyTimerInterval) {
            clearInterval(this.lobbyTimerInterval);
            this.lobbyTimerInterval = null;
        }
    },
    
    updateLobbyScene(deltaTime) {
        // Update chat
        this.updateLobbyChat();
    },
    
    renderLobbyScene() {
        // Lobby rendering is handled by DOM
    },
    
    createLobbyUI() {
        // Header
        const header = Utils.DOM.createElement('div', 'lobby-header', {
            textAlign: 'center',
            marginBottom: '30px'
        });
        
        const title = Utils.DOM.createElement('h1', '', {
            fontSize: '2rem',
            marginBottom: '10px'
        });
        title.textContent = 'Waiting for Players';
        
        this.playerCountDisplay = Utils.DOM.createElement('div', '', {
            fontSize: '1.2rem',
            color: '#3498db'
        });
        this.updatePlayerCountDisplay();
        
        this.timerDisplay = Utils.DOM.createElement('div', '', {
            fontSize: '1.5rem',
            color: '#e74c3c',
            marginTop: '10px'
        });
        
        header.appendChild(title);
        header.appendChild(this.playerCountDisplay);
        header.appendChild(this.timerDisplay);
        
        // Main content area
        const content = Utils.DOM.createElement('div', 'lobby-content', {
            display: 'flex',
            height: 'calc(100% - 150px)',
            gap: '20px'
        });
        
        // Players list
        const playersSection = Utils.DOM.createElement('div', 'players-section', {
            flex: '1',
            backgroundColor: '#2c3e50',
            borderRadius: '10px',
            padding: '20px'
        });
        
        const playersTitle = Utils.DOM.createElement('h3', '', {
            marginBottom: '15px',
            textAlign: 'center'
        });
        playersTitle.textContent = 'Players';
        
        this.playersList = Utils.DOM.createElement('div', 'players-list', {
            display: 'flex',
            flexDirection: 'column',
            gap: '10px'
        });
        
        playersSection.appendChild(playersTitle);
        playersSection.appendChild(this.playersList);
        
        // Chat section
        const chatSection = Utils.DOM.createElement('div', 'chat-section', {
            flex: '1',
            backgroundColor: '#2c3e50',
            borderRadius: '10px',
            padding: '20px',
            display: 'flex',
            flexDirection: 'column'
        });
        
        const chatTitle = Utils.DOM.createElement('h3', '', {
            marginBottom: '15px',
            textAlign: 'center'
        });
        chatTitle.textContent = 'Chat';
        
        this.chatMessages = Utils.DOM.createElement('div', 'chat-messages', {
            flex: '1',
            backgroundColor: '#34495e',
            borderRadius: '5px',
            padding: '10px',
            overflowY: 'auto',
            marginBottom: '10px',
            minHeight: '200px'
        });
        
        const chatInputContainer = Utils.DOM.createElement('div', 'chat-input-container', {
            display: 'flex',
            gap: '10px'
        });
        
        this.chatInput = Utils.DOM.createElement('input', 'chat-input', {
            flex: '1',
            padding: '10px',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: '#34495e',
            color: 'white'
        });
        this.chatInput.type = 'text';
        this.chatInput.placeholder = 'Type a message...';
        this.chatInput.maxLength = 200;
        
        const sendButton = Utils.DOM.createElement('button', 'send-button', {
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: '#3498db',
            color: 'white',
            cursor: 'pointer'
        });
        sendButton.textContent = 'Send';
        
        // Chat event handlers
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });
        
        sendButton.addEventListener('click', () => {
            this.sendChatMessage();
        });
        
        chatInputContainer.appendChild(this.chatInput);
        chatInputContainer.appendChild(sendButton);
        
        chatSection.appendChild(chatTitle);
        chatSection.appendChild(this.chatMessages);
        chatSection.appendChild(chatInputContainer);
        
        content.appendChild(playersSection);
        content.appendChild(chatSection);

        // Add action buttons
        const buttonSection = Utils.DOM.createElement('div', 'lobby-buttons', {
            display: 'flex',
            justifyContent: 'center',
            gap: '15px',
            marginTop: '20px'
        });

        const changeLevelButton = this.createMenuButton('Change Level', () => {
            this.framework.switchScene('levelSelect');
        }, '#f39c12');

        const leaveButton = this.createMenuButton('Leave Lobby', () => {
            if (this.multiplayerManager) {
                this.multiplayerManager.leaveRoom();
            }
            this.framework.switchScene('menu');
        }, '#e74c3c');

        buttonSection.appendChild(changeLevelButton);
        buttonSection.appendChild(leaveButton);

        this.lobbyContainer.appendChild(header);
        this.lobbyContainer.appendChild(content);
        this.lobbyContainer.appendChild(buttonSection);
        
        // Update players list
        this.updatePlayersList();
    },
    
    updatePlayerCountDisplay() {
        if (this.playerCountDisplay) {
            const count = this.multiplayerManager ? this.multiplayerManager.getPlayerCount() : 1;
            this.playerCountDisplay.textContent = `Players: ${count}/4`;
        }
    },
    
    updatePlayersList() {
        if (!this.playersList) return;
        
        this.playersList.innerHTML = '';
        
        if (this.multiplayerManager) {
            const players = this.multiplayerManager.getPlayers();
            players.forEach((player, index) => {
                const playerElement = Utils.DOM.createElement('div', 'player-item', {
                    padding: '10px',
                    backgroundColor: '#34495e',
                    borderRadius: '5px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px'
                });
                
                const playerColor = Utils.DOM.createElement('div', 'player-color', {
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    backgroundColor: this.getPlayerColor(index)
                });
                
                const playerName = Utils.DOM.createElement('span', 'player-name');
                playerName.textContent = player.nickname || `Player ${index + 1}`;
                
                playerElement.appendChild(playerColor);
                playerElement.appendChild(playerName);
                this.playersList.appendChild(playerElement);
            });
        } else {
            // Local player only
            const playerElement = Utils.DOM.createElement('div', 'player-item', {
                padding: '10px',
                backgroundColor: '#34495e',
                borderRadius: '5px'
            });
            playerElement.textContent = this.playerNickname || 'Player 1';
            this.playersList.appendChild(playerElement);
        }
    },
    
    updateLobbyTimer() {
        const playerCount = this.multiplayerManager ? this.multiplayerManager.getPlayerCount() : 1;
        
        if (!this.isCountingDown) {
            if (playerCount >= 4) {
                // Start countdown immediately with 4 players
                this.startCountdown();
            } else if (playerCount >= 2) {
                this.lobbyTimer--;
                if (this.lobbyTimer <= 0) {
                    this.startCountdown();
                } else {
                    this.timerDisplay.textContent = `Game starts in ${this.lobbyTimer}s (waiting for more players)`;
                }
            } else {
                this.timerDisplay.textContent = 'Waiting for at least 2 players...';
            }
        } else {
            this.countdownTimer--;
            if (this.countdownTimer <= 0) {
                this.startGame();
            } else {
                this.timerDisplay.textContent = `Game starting in ${this.countdownTimer}s!`;
            }
        }
    },
    
    startCountdown() {
        this.isCountingDown = true;
        this.countdownTimer = 10;
        this.timerDisplay.textContent = `Game starting in ${this.countdownTimer}s!`;
    },
    
    startGame() {
        console.log('Starting game from lobby...');
        this.framework.switchScene('game');
    },

    addSelectedLevelInfo() {
        if (!this.selectedLevel) return;

        const levelInfo = Utils.DOM.createElement('div', 'selected-level-info', {
            position: 'absolute',
            top: '20px',
            right: '20px',
            backgroundColor: '#34495e',
            border: '2px solid #3498db',
            borderRadius: '10px',
            padding: '15px',
            minWidth: '200px',
            color: 'white'
        });

        const levelTitle = Utils.DOM.createElement('h3', 'level-title', {
            margin: '0 0 10px 0',
            color: '#3498db',
            fontSize: '1.2rem'
        });
        levelTitle.textContent = 'Selected Level';

        const levelName = Utils.DOM.createElement('div', 'level-name', {
            fontWeight: 'bold',
            fontSize: '1.1rem',
            marginBottom: '5px'
        });
        levelName.textContent = this.selectedLevel.name;

        const levelDifficulty = Utils.DOM.createElement('div', 'level-difficulty', {
            fontSize: '0.9rem',
            color: this.getDifficultyColor(this.selectedLevel.difficulty),
            marginBottom: '5px'
        });
        levelDifficulty.textContent = `Difficulty: ${this.selectedLevel.difficulty}`;

        const levelDescription = Utils.DOM.createElement('div', 'level-description', {
            fontSize: '0.8rem',
            color: '#bdc3c7',
            lineHeight: '1.3'
        });
        levelDescription.textContent = this.selectedLevel.description;

        levelInfo.appendChild(levelTitle);
        levelInfo.appendChild(levelName);
        levelInfo.appendChild(levelDifficulty);
        levelInfo.appendChild(levelDescription);

        this.lobbyContainer.appendChild(levelInfo);
    },
    
    sendChatMessage() {
        const message = this.chatInput.value.trim();
        if (message) {
            if (this.multiplayerManager) {
                this.multiplayerManager.sendChatMessage(message);
            } else {
                // Local chat message
                this.addChatMessage({
                    nickname: this.playerNickname,
                    message: message,
                    timestamp: Date.now()
                });
            }
            this.chatInput.value = '';
        }
    },
    
    addChatMessage(data) {
        if (!this.chatMessages) return;

        const messageElement = Utils.DOM.createElement('div', 'chat-message', {
            marginBottom: '5px',
            padding: '5px',
            borderRadius: '3px',
            backgroundColor: data.isSystem ? '#34495e' : '#2c3e50'
        });

        const time = new Date(data.timestamp).toLocaleTimeString();

        if (data.isSystem) {
            messageElement.innerHTML = `<span style="color: #3498db;">[${time}] ${data.message}</span>`;
        } else {
            messageElement.innerHTML = `<strong style="color: #f39c12;">${data.nickname}</strong> <span style="color: #bdc3c7;">[${time}]</span>: ${data.message}`;
        }

        this.chatMessages.appendChild(messageElement);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    },
    
    updateLobbyChat() {
        if (this.multiplayerManager) {
            const messages = this.multiplayerManager.getChatMessages();
            // Update chat display if needed
        }
    },

    setupChatEventListeners() {
        // Listen for chat messages from multiplayer manager
        this.framework.on('chatMessage', (data) => {
            console.log('Lobby scene received chat message:', data);
            this.addChatMessage(data);
        });

        // Listen for player join/leave events
        this.framework.on('playerJoined', (data) => {
            console.log('Player joined lobby:', data);
            this.addChatMessage({
                nickname: 'System',
                message: `${data.nickname} joined the game!`,
                timestamp: Date.now(),
                isSystem: true
            });
            this.updatePlayersList();
        });

        this.framework.on('playerLeft', (data) => {
            console.log('Player left lobby:', data);
            this.addChatMessage({
                nickname: 'System',
                message: `A player left the game.`,
                timestamp: Date.now(),
                isSystem: true
            });
            this.updatePlayersList();
        });

        // Listen for connection events
        this.framework.on('connectionEstablished', () => {
            this.addChatMessage({
                nickname: 'System',
                message: 'Connected to multiplayer server!',
                timestamp: Date.now(),
                isSystem: true
            });
        });

        this.framework.on('connectionLost', () => {
            this.addChatMessage({
                nickname: 'System',
                message: 'Connection lost. Trying to reconnect...',
                timestamp: Date.now(),
                isSystem: true
            });
        });
    },
    
    // Utility methods
    createMenuButton(text, onClick, backgroundColor = '#3498db') {
        const button = Utils.DOM.createElement('button', 'menu-button', {
            padding: '15px 30px',
            fontSize: '1.1rem',
            border: 'none',
            borderRadius: '5px',
            backgroundColor: backgroundColor,
            color: 'white',
            cursor: 'pointer',
            textTransform: 'uppercase',
            letterSpacing: '2px',
            fontFamily: 'EngraversGothic, sans-serif',
            transition: 'all 0.3s ease'
        });
        
        button.textContent = text;
        button.addEventListener('click', onClick);
        
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = 'scale(1)';
            button.style.boxShadow = 'none';
        });
        
        return button;
    },
    
    getPlayerColor(index) {
        const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
        return colors[index % colors.length];
    }
        }); // End of Object.assign
    } // End of extendBombermanGame function

    // Start the extension process
    extendBombermanGame();
})(); // End of IIFE
