/**
 * Bomb and Explosion System for Bomberman
 */

class Bomb {
    constructor(game, bombId, gridX, gridY, range, owner) {
        this.game = game;
        this.bombId = bombId;
        this.gridX = gridX;
        this.gridY = gridY;
        this.range = range;
        this.owner = owner;
        this.timer = game.gameConfig.bombTimer; // 3 seconds
        this.element = null;
        this.entityId = bombId;
        this.hasExploded = false;
        
        this.init();
    }
    
    init() {
        this.createBombEntity();
        this.startTimer();
    }
    
    createBombEntity() {
        // Create bomb entity in framework
        const entity = this.game.framework.createEntity(this.entityId);
        
        // Create DOM element
        this.element = Utils.DOM.createElement('div', 'bomb', {
            position: 'absolute',
            width: (this.game.gameConfig.cellSize - 6) + 'px',
            height: (this.game.gameConfig.cellSize - 6) + 'px',
            borderRadius: '50%',
            zIndex: '15'
        });
        
        // Add bomb timer display
        const timerDisplay = Utils.DOM.createElement('div', 'bomb-timer', {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '14px',
            textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
        });
        this.element.appendChild(timerDisplay);
        
        // Position bomb
        const pixelPos = this.game.gameMap.gridToPixel(this.gridX, this.gridY);
        this.element.style.left = (pixelPos.x + 3) + 'px';
        this.element.style.top = (pixelPos.y + 3) + 'px';
        
        // Add to game map
        this.game.gameMap.mapContainer.appendChild(this.element);
        
        // Add components to entity
        this.game.framework.addComponent(this.entityId, 'Transform', 
            new Components.Transform(pixelPos.x, pixelPos.y)
        );
        
        this.game.framework.addComponent(this.entityId, 'DOMRenderer', 
            new Components.DOMRenderer(this.element, 'bomb')
        );
        
        this.game.framework.addComponent(this.entityId, 'Collider',
            new Components.Collider(
                this.game.gameConfig.cellSize,
                this.game.gameConfig.cellSize,
                0, 0
            )
        );
        
        // Add timer component
        this.game.framework.addComponent(this.entityId, 'Timer',
            new Components.Timer(this.timer / 1000, () => this.explode(), false)
        );
        
        const timer = this.game.framework.getComponent(this.entityId, 'Timer');
        timer.start();
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            this.timer -= 100;
            this.updateTimerDisplay();
            
            if (this.timer <= 0) {
                clearInterval(this.timerInterval);
                this.explode();
            }
        }, 100);
    }
    
    updateTimerDisplay() {
        const timerDisplay = this.element.querySelector('.bomb-timer');
        if (timerDisplay) {
            const seconds = Math.ceil(this.timer / 1000);
            timerDisplay.textContent = seconds;
            
            // Change color as timer gets low
            if (seconds <= 1) {
                timerDisplay.style.color = '#e74c3c';
            } else if (seconds <= 2) {
                timerDisplay.style.color = '#f39c12';
            }
        }
    }
    
    explode() {
        if (this.hasExploded) return;
        
        this.hasExploded = true;
        
        // Clear timer
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        
        // Create explosion
        const explosion = new Explosion(this.game, this.gridX, this.gridY, this.range, this.owner);
        
        // Remove bomb
        this.destroy();
        
        // Decrease owner's active bomb count
        if (this.owner) {
            this.owner.activeBombs--;
        }
        
        // Remove from game bombs map
        this.game.bombs.delete(this.bombId);
        
        // Notify multiplayer
        if (this.game.multiplayerManager) {
            this.game.multiplayerManager.sendMessage('bombExploded', {
                bombId: this.bombId,
                x: this.gridX,
                y: this.gridY,
                range: this.range,
                ownerId: this.owner ? this.owner.playerId : null
            });
        }
        
        // Emit event
        this.game.framework.emit('bombExploded', {
            bombId: this.bombId,
            x: this.gridX,
            y: this.gridY,
            range: this.range
        });
    }
    
    destroy() {
        this.game.framework.removeEntity(this.entityId);
        
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }
}

class Explosion {
    constructor(game, centerX, centerY, range, owner) {
        this.game = game;
        this.centerX = centerX;
        this.centerY = centerY;
        this.range = range;
        this.owner = owner;
        this.explosionCells = [];
        this.explosionElements = [];
        this.duration = 500; // 0.5 seconds
        
        this.init();
    }
    
    init() {
        this.calculateExplosionCells();
        this.createExplosionVisuals();
        this.dealDamage();
        this.startCleanupTimer();
    }
    
    calculateExplosionCells() {
        // Center explosion
        this.explosionCells.push({ x: this.centerX, y: this.centerY });
        
        // Explosion rays in four directions
        const directions = [
            { dx: 0, dy: -1 }, // Up
            { dx: 0, dy: 1 },  // Down
            { dx: -1, dy: 0 }, // Left
            { dx: 1, dy: 0 }   // Right
        ];
        
        directions.forEach(dir => {
            for (let i = 1; i <= this.range; i++) {
                const x = this.centerX + dir.dx * i;
                const y = this.centerY + dir.dy * i;
                
                // Check if position is valid
                if (!this.game.gameMap.isValidPosition(x, y)) {
                    break;
                }
                
                // Check if there's a wall
                if (this.game.gameMap.isWall(x, y)) {
                    break;
                }
                
                // Add explosion cell
                this.explosionCells.push({ x, y });
                
                // Check if there's a destructible block
                if (this.game.gameMap.isDestructible(x, y)) {
                    this.game.gameMap.destroyBlock(x, y);
                    break; // Explosion stops at destroyed block
                }
                
                // Check for other bombs (chain reaction)
                const bombId = `bomb-${x}-${y}`;
                if (this.game.bombs.has(bombId)) {
                    const bomb = this.game.bombs.get(bombId);
                    // Trigger chain explosion after a short delay
                    setTimeout(() => {
                        if (!bomb.hasExploded) {
                            bomb.explode();
                        }
                    }, 100);
                }
            }
        });
    }
    
    createExplosionVisuals() {
        this.explosionCells.forEach((cell, index) => {
            const explosionElement = Utils.DOM.createElement('div', 'explosion', {
                position: 'absolute',
                width: this.game.gameConfig.cellSize + 'px',
                height: this.game.gameConfig.cellSize + 'px',
                zIndex: '25'
            });
            
            // Position explosion
            const pixelPos = this.game.gameMap.gridToPixel(cell.x, cell.y);
            explosionElement.style.left = pixelPos.x + 'px';
            explosionElement.style.top = pixelPos.y + 'px';
            
            // Add to game map
            this.game.gameMap.mapContainer.appendChild(explosionElement);
            this.explosionElements.push(explosionElement);
            
            // Create explosion entity for collision detection
            const explosionId = `explosion-${cell.x}-${cell.y}-${Date.now()}`;
            const entity = this.game.framework.createEntity(explosionId);
            
            this.game.framework.addComponent(explosionId, 'Transform', 
                new Components.Transform(pixelPos.x, pixelPos.y)
            );
            
            this.game.framework.addComponent(explosionId, 'DOMRenderer', 
                new Components.DOMRenderer(explosionElement, 'explosion')
            );
            
            this.game.framework.addComponent(explosionId, 'Collider',
                new Components.Collider(
                    this.game.gameConfig.cellSize,
                    this.game.gameConfig.cellSize,
                    0, 0
                )
            );
            
            // Store entity ID for cleanup
            explosionElement.dataset.entityId = explosionId;
        });
    }
    
    dealDamage() {
        // Check for players in explosion cells
        this.explosionCells.forEach(cell => {
            // Check all players
            this.game.players.forEach(player => {
                if (player.isAlive) {
                    const playerGrid = player.getGridPosition();
                    
                    // Check if player is in explosion cell
                    if (playerGrid.x === cell.x && playerGrid.y === cell.y) {
                        player.takeDamage();
                    }
                }
            });
        });
    }
    
    startCleanupTimer() {
        setTimeout(() => {
            this.cleanup();
        }, this.duration);
    }
    
    cleanup() {
        // Remove explosion elements
        this.explosionElements.forEach(element => {
            const entityId = element.dataset.entityId;
            if (entityId) {
                this.game.framework.removeEntity(entityId);
            }
            
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
        
        this.explosionElements = [];
        this.explosionCells = [];
    }
    
    // Check if a position is in the explosion
    isInExplosion(x, y) {
        return this.explosionCells.some(cell => cell.x === x && cell.y === y);
    }
    
    // Get all explosion cells
    getExplosionCells() {
        return [...this.explosionCells];
    }
}

// Export for use
window.Bomb = Bomb;
window.Explosion = Explosion;
