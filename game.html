<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman - Multiplayer Game</title>
    <link rel="stylesheet" href="./static/style.css">
    <style>
        /* Additional game-specific styles */
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #07131F;
            font-family: 'EngraversGothic', sans-serif;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #07131F;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 10000;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #34495e;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10001;
            display: none;
        }
        
        .controls-help {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 100;
            display: none;
        }
        
        .controls-help.show {
            display: block;
        }
        
        .controls-help h4 {
            margin: 0 0 10px 0;
            color: #3498db;
        }
        
        .controls-help p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <h2>Loading Bomberman...</h2>
        <p>Initializing game framework...</p>
    </div>
    
    <!-- Error Message -->
    <div id="error-message" class="error-message">
        <h3>Error</h3>
        <p id="error-text">An error occurred while loading the game.</p>
        <button onclick="location.reload()">Reload Game</button>
    </div>
    
    <!-- Controls Help -->
    <div id="controls-help" class="controls-help">
        <h4>Controls</h4>
        <p><strong>Move:</strong> Arrow Keys or WASD</p>
        <p><strong>Place Bomb:</strong> Spacebar</p>
        <p><strong>Toggle Help:</strong> F1 (Performance) / H (Controls)</p>
        <p><strong>Chat:</strong> Enter message in lobby</p>
    </div>
    
    <!-- Framework Scripts -->
    <script src="./static/js/framework/utils.js"></script>
    <script src="./static/js/framework/components.js"></script>
    <script src="./static/js/framework/systems.js"></script>
    <script src="./static/js/framework/core.js"></script>
    
    <!-- Game Scripts -->
    <script src="./static/js/game/multiplayer.js"></script>
    <script src="./static/js/game/map.js"></script>
    <script src="./static/js/game/player.js"></script>
    <script src="./static/js/game/bomb.js"></script>
    <script src="./static/js/game/scenes.js"></script>
    <script src="./static/js/game/gameScene.js"></script>
    <script src="./static/js/game/game.js"></script>
    
    <script>
        // Game initialization
        let game = null;
        let loadingScreen = null;
        let errorMessage = null;
        let controlsHelp = null;
        
        // Initialize game when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadingScreen = document.getElementById('loading-screen');
            errorMessage = document.getElementById('error-message');
            controlsHelp = document.getElementById('controls-help');
            
            // Setup global error handling
            window.addEventListener('error', function(e) {
                showError('JavaScript Error: ' + e.message);
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                showError('Promise Rejection: ' + e.reason);
            });
            
            // Setup controls help toggle
            document.addEventListener('keydown', function(e) {
                if (e.key === 'h' || e.key === 'H') {
                    toggleControlsHelp();
                }
            });
            
            // Initialize game
            initializeGame();
        });
        
        function initializeGame() {
            try {
                updateLoadingText('Creating game instance...');
                
                // Small delay to show loading screen
                setTimeout(() => {
                    try {
                        // Create game instance
                        game = BombermanGame.start();
                        
                        updateLoadingText('Game initialized successfully!');
                        
                        // Hide loading screen after a short delay
                        setTimeout(() => {
                            hideLoadingScreen();
                            showControlsHelp();
                        }, 1000);
                        
                    } catch (error) {
                        console.error('Game initialization error:', error);
                        showError('Failed to initialize game: ' + error.message);
                    }
                }, 500);
                
            } catch (error) {
                console.error('Critical initialization error:', error);
                showError('Critical error during initialization: ' + error.message);
            }
        }
        
        function updateLoadingText(text) {
            const loadingText = loadingScreen.querySelector('p');
            if (loadingText) {
                loadingText.textContent = text;
            }
        }
        
        function hideLoadingScreen() {
            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }
        }
        
        function showError(message) {
            console.error(message);
            
            if (errorMessage) {
                document.getElementById('error-text').textContent = message;
                errorMessage.style.display = 'block';
            }
            
            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }
        }
        
        function toggleControlsHelp() {
            if (controlsHelp) {
                controlsHelp.classList.toggle('show');
            }
        }
        
        function showControlsHelp() {
            if (controlsHelp) {
                controlsHelp.classList.add('show');
                
                // Auto-hide after 5 seconds
                setTimeout(() => {
                    controlsHelp.classList.remove('show');
                }, 5000);
            }
        }
        
        // Prevent context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', function(e) {
            if (['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
                e.preventDefault();
            }
        });
        
        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (game && game.framework) {
                if (document.hidden) {
                    game.framework.pause();
                } else {
                    game.framework.resume();
                }
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', function() {
            // Game should handle responsive design through CSS
            console.log('Window resized');
        });
        
        // Export game instance for debugging
        window.game = game;
        
        console.log('Bomberman game script loaded');
    </script>
</body>
</html>
