/**
 * Component definitions for the game framework
 */

// Transform component for position, rotation, scale
class Transform {
    constructor(x = 0, y = 0, rotation = 0, scaleX = 1, scaleY = 1) {
        this.x = x;
        this.y = y;
        this.rotation = rotation;
        this.scaleX = scaleX;
        this.scaleY = scaleY;
        this.prevX = x;
        this.prevY = y;
    }
    
    setPosition(x, y) {
        this.prevX = this.x;
        this.prevY = this.y;
        this.x = x;
        this.y = y;
    }
    
    translate(dx, dy) {
        this.setPosition(this.x + dx, this.y + dy);
    }
    
    hasChanged() {
        return this.x !== this.prevX || this.y !== this.prevY;
    }
}

// Render component for DOM elements
class DOMRenderer {
    constructor(element, className = '') {
        this.element = element;
        this.className = className;
        this.visible = true;
        this.zIndex = 0;
        this.opacity = 1;
        this.needsUpdate = true;
        
        if (className) {
            this.element.className = className;
        }
        
        // Set initial styles for performance
        this.element.style.position = 'absolute';
        this.element.style.willChange = 'transform';
        this.element.style.backfaceVisibility = 'hidden';
    }
    
    setVisible(visible) {
        if (this.visible !== visible) {
            this.visible = visible;
            this.needsUpdate = true;
        }
    }
    
    setOpacity(opacity) {
        if (this.opacity !== opacity) {
            this.opacity = opacity;
            this.needsUpdate = true;
        }
    }
    
    setZIndex(zIndex) {
        if (this.zIndex !== zIndex) {
            this.zIndex = zIndex;
            this.needsUpdate = true;
        }
    }
    
    updateElement(transform) {
        if (this.needsUpdate || transform.hasChanged()) {
            this.element.style.transform = `translate(${transform.x}px, ${transform.y}px) rotate(${transform.rotation}deg) scale(${transform.scaleX}, ${transform.scaleY})`;
            this.element.style.display = this.visible ? 'block' : 'none';
            this.element.style.opacity = this.opacity;
            this.element.style.zIndex = this.zIndex;
            this.needsUpdate = false;
        }
    }
}

// Physics component for movement and collision
class Physics {
    constructor(velocityX = 0, velocityY = 0, mass = 1) {
        this.velocityX = velocityX;
        this.velocityY = velocityY;
        this.mass = mass;
        this.friction = 0.8;
        this.maxSpeed = 300; // pixels per second
        this.acceleration = 800; // pixels per second squared
    }
    
    applyForce(forceX, forceY) {
        this.velocityX += forceX / this.mass;
        this.velocityY += forceY / this.mass;
    }
    
    update(deltaTime) {
        // Apply friction
        this.velocityX *= this.friction;
        this.velocityY *= this.friction;
        
        // Clamp to max speed
        const speed = Math.sqrt(this.velocityX * this.velocityX + this.velocityY * this.velocityY);
        if (speed > this.maxSpeed) {
            this.velocityX = (this.velocityX / speed) * this.maxSpeed;
            this.velocityY = (this.velocityY / speed) * this.maxSpeed;
        }
        
        // Stop very small movements to prevent jitter
        if (Math.abs(this.velocityX) < 0.1) this.velocityX = 0;
        if (Math.abs(this.velocityY) < 0.1) this.velocityY = 0;
    }
}

// Collision component for hit detection
class Collider {
    constructor(width, height, offsetX = 0, offsetY = 0, type = 'rectangle') {
        this.width = width;
        this.height = height;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
        this.type = type;
        this.isTrigger = false;
        this.layer = 'default';
        this.tags = new Set();
    }
    
    addTag(tag) {
        this.tags.add(tag);
    }
    
    removeTag(tag) {
        this.tags.delete(tag);
    }
    
    hasTag(tag) {
        return this.tags.has(tag);
    }
    
    getBounds(transform) {
        return {
            left: transform.x + this.offsetX,
            top: transform.y + this.offsetY,
            right: transform.x + this.offsetX + this.width,
            bottom: transform.y + this.offsetY + this.height,
            centerX: transform.x + this.offsetX + this.width / 2,
            centerY: transform.y + this.offsetY + this.height / 2
        };
    }
    
    intersects(otherCollider, transform, otherTransform) {
        const bounds1 = this.getBounds(transform);
        const bounds2 = otherCollider.getBounds(otherTransform);
        
        return !(bounds1.right <= bounds2.left || 
                bounds1.left >= bounds2.right || 
                bounds1.bottom <= bounds2.top || 
                bounds1.top >= bounds2.bottom);
    }
}

// Input component for handling user input
class Input {
    constructor() {
        this.keys = new Map();
        this.mouseX = 0;
        this.mouseY = 0;
        this.mouseButtons = new Map();
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            this.keys.set(e.code, true);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys.set(e.code, false);
        });
        
        document.addEventListener('mousemove', (e) => {
            this.mouseX = e.clientX;
            this.mouseY = e.clientY;
        });
        
        document.addEventListener('mousedown', (e) => {
            this.mouseButtons.set(e.button, true);
        });
        
        document.addEventListener('mouseup', (e) => {
            this.mouseButtons.set(e.button, false);
        });
    }
    
    isKeyPressed(keyCode) {
        return this.keys.get(keyCode) || false;
    }
    
    isMouseButtonPressed(button) {
        return this.mouseButtons.get(button) || false;
    }
    
    getMousePosition() {
        return { x: this.mouseX, y: this.mouseY };
    }
}

// Animation component for sprite animations
class Animation {
    constructor(frames = [], frameRate = 10) {
        this.frames = frames;
        this.frameRate = frameRate;
        this.currentFrame = 0;
        this.frameTime = 0;
        this.playing = false;
        this.loop = true;
        this.onComplete = null;
    }
    
    play() {
        this.playing = true;
        this.currentFrame = 0;
        this.frameTime = 0;
    }
    
    stop() {
        this.playing = false;
        this.currentFrame = 0;
        this.frameTime = 0;
    }
    
    pause() {
        this.playing = false;
    }
    
    update(deltaTime) {
        if (!this.playing || this.frames.length === 0) return;
        
        this.frameTime += deltaTime;
        const frameDuration = 1 / this.frameRate;
        
        if (this.frameTime >= frameDuration) {
            this.frameTime = 0;
            this.currentFrame++;
            
            if (this.currentFrame >= this.frames.length) {
                if (this.loop) {
                    this.currentFrame = 0;
                } else {
                    this.currentFrame = this.frames.length - 1;
                    this.playing = false;
                    if (this.onComplete) {
                        this.onComplete();
                    }
                }
            }
        }
    }
    
    getCurrentFrame() {
        return this.frames[this.currentFrame] || null;
    }
}

// Timer component for delayed actions
class Timer {
    constructor(duration, callback, repeat = false) {
        this.duration = duration;
        this.callback = callback;
        this.repeat = repeat;
        this.currentTime = 0;
        this.active = false;
    }
    
    start() {
        this.active = true;
        this.currentTime = 0;
    }
    
    stop() {
        this.active = false;
        this.currentTime = 0;
    }
    
    pause() {
        this.active = false;
    }
    
    resume() {
        this.active = true;
    }
    
    update(deltaTime) {
        if (!this.active) return;
        
        this.currentTime += deltaTime;
        
        if (this.currentTime >= this.duration) {
            if (this.callback) {
                this.callback();
            }
            
            if (this.repeat) {
                this.currentTime = 0;
            } else {
                this.active = false;
            }
        }
    }
    
    getProgress() {
        return Math.min(this.currentTime / this.duration, 1);
    }
}

// Export components
window.Components = {
    Transform,
    DOMRenderer,
    Physics,
    Collider,
    Input,
    Animation,
    Timer
};
