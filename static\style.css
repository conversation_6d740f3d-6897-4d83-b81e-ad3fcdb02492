Body{
    background-color: #07131F;
    font-family: 'EngraversGothic', sans-serif;
    margin: 0 auto;
    padding: 0;
}

@font-face {
    font-family: 'EngraversGothic';
    src: url('./fonts/Engravers.otf') format('opentype'); /* Updated path */
    font-weight: normal;
    font-style: normal;
}

.splash-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
}

.splash-screen{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.character-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.splash-screen .logo-container img{
    width: 70vw;
    height: auto;
    animation: fadeIn 2s ease-in-out;
}

.splash-screen .menu{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -70px;
}

.splash-screen .menu a{
    font-family: 'EngraversGothic', sans-serif;
    text-transform: uppercase;
    letter-spacing: 9px;
    color: white;
    font-family: 'EngraversGothic', serif;
    text-decoration: none;
    margin-bottom: 14px;
    font-size: 1.3rem;
    opacity: 0.75;
    transition: opacity 0.3s ease-in-out;
}

.splash-screen .menu a:hover{
    opacity: 1;
}

.character-container .heading{
    margin-top: 5vh;
}

.img-header{
    width: 35vw;
    height: auto;
    max-width: 600px;
    margin-bottom: 20px;
}

.character-selection{
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    justify-content: center;
    width: 70vw;
}

.character-container .character-selection .grid{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 0px;
    font-family: 'EngraversGothic', sans-serif;
    text-transform: uppercase;
    letter-spacing: 9px;
    color: white;
    text-decoration: none;
    margin-bottom: 14px;
    font-size: 0.8rem;
    width: 100%;
}

.character-container .character-selection .grid .character{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease-in-out;
}

.character-container .character-selection .grid .character .image-placeholder{
    width: 220px;
    height: 290px;
    background-color: #dedede;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
    margin-bottom: 20px;
}

.character-container .character-selection .grid .character:hover .image-placeholder{
    box-shadow: rgba(178, 117, 208, 0.4) 0px 5px, rgba(178, 117, 208, 0.3) 0px 10px, rgba(178, 117, 208, 0.2) 0px 15px, rgba(178, 117, 208, 0.1) 0px 20px, rgba(178, 117, 208, 0.05) 0px 25px;
}

@media screen and (max-width: 900px) {
    .splash-screen .menu{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 30px;
    }
    .splash-screen .logo-container img{
        width: 90vw;
        height: auto;
        animation: fadeIn 2s ease-in-out;
    }
}