Body{
    background-color: #07131F;
    font-family: 'EngraversGothic', sans-serif;
    margin: 0 auto;
    padding: 0;
}

@font-face {
    font-family: 'EngraversGothic';
    src: url('./fonts/Engravers.otf') format('opentype'); /* Updated path */
    font-weight: normal;
    font-style: normal;
}

.splash-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
}

.splash-screen{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.character-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.splash-screen .logo-container img{
    width: 70vw;
    height: auto;
    animation: fadeIn 2s ease-in-out;
}

.splash-screen .menu{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -70px;
}

.splash-screen .menu a{
    font-family: 'EngraversGothic', sans-serif;
    text-transform: uppercase;
    letter-spacing: 9px;
    color: white;
    font-family: 'EngraversGothic', serif;
    text-decoration: none;
    margin-bottom: 14px;
    font-size: 1.3rem;
    opacity: 0.75;
    transition: opacity 0.3s ease-in-out;
}

.splash-screen .menu a:hover{
    opacity: 1;
}

.character-container .heading{
    margin-top: 5vh;
}

.img-header{
    width: 35vw;
    height: auto;
    max-width: 600px;
    margin-bottom: 20px;
}

.character-selection{
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    justify-content: center;
    width: 70vw;
}

.character-container .character-selection .grid{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 0px;
    font-family: 'EngraversGothic', sans-serif;
    text-transform: uppercase;
    letter-spacing: 9px;
    color: white;
    text-decoration: none;
    margin-bottom: 14px;
    font-size: 0.8rem;
    width: 100%;
}

.character-container .character-selection .grid .character{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease-in-out;
}

.character-container .character-selection .grid .character .image-placeholder{
    width: 220px;
    height: 290px;
    background-color: #dedede;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
    margin-bottom: 20px;
}

.character-container .character-selection .grid .character:hover .image-placeholder{
    box-shadow: rgba(178, 117, 208, 0.4) 0px 5px, rgba(178, 117, 208, 0.3) 0px 10px, rgba(178, 117, 208, 0.2) 0px 15px, rgba(178, 117, 208, 0.1) 0px 20px, rgba(178, 117, 208, 0.05) 0px 25px;
}

/* Game Map Styles */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.game-map {
    position: absolute;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.map-cell {
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.2s ease;
}

.map-cell.empty {
    background-color: #34495e;
}

.map-cell.wall {
    background: linear-gradient(45deg, #7f8c8d, #95a5a6);
    border: 2px solid #bdc3c7;
    box-shadow: inset 2px 2px 4px rgba(0,0,0,0.3);
}

.map-cell.block {
    background: linear-gradient(45deg, #8b4513, #a0522d);
    border: 2px solid #cd853f;
    box-shadow: inset 2px 2px 4px rgba(0,0,0,0.3);
    position: relative;
}

.map-cell.block::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(0,0,0,0.1) 2px,
        rgba(0,0,0,0.1) 4px
    );
}

.map-cell.spawn {
    background: radial-gradient(circle, #3498db, #2980b9);
    border: 2px solid #5dade2;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

/* Power-up styles */
.powerup {
    z-index: 10;
    cursor: pointer;
}

.powerup.bombs {
    background: radial-gradient(circle, #e74c3c, #c0392b);
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.6);
}

.powerup.flames {
    background: radial-gradient(circle, #f39c12, #e67e22);
    box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
}

.powerup.speed {
    background: radial-gradient(circle, #2ecc71, #27ae60);
    box-shadow: 0 0 15px rgba(46, 204, 113, 0.6);
}

@keyframes powerUpPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

/* Player styles */
.player {
    z-index: 20;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
    transition: transform 0.1s ease;
}

.player.player-1 {
    background: radial-gradient(circle, #e74c3c, #c0392b);
}

.player.player-2 {
    background: radial-gradient(circle, #3498db, #2980b9);
}

.player.player-3 {
    background: radial-gradient(circle, #2ecc71, #27ae60);
}

.player.player-4 {
    background: radial-gradient(circle, #f39c12, #e67e22);
}

/* Bomb styles */
.bomb {
    z-index: 15;
    border-radius: 50%;
    background: radial-gradient(circle, #2c3e50, #34495e);
    border: 3px solid #e74c3c;
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.8);
    animation: bombPulse 0.5s infinite alternate;
}

@keyframes bombPulse {
    0% { transform: scale(1); box-shadow: 0 0 15px rgba(231, 76, 60, 0.8); }
    100% { transform: scale(1.1); box-shadow: 0 0 25px rgba(231, 76, 60, 1); }
}

/* Explosion styles */
.explosion {
    z-index: 25;
    background: radial-gradient(circle, #f39c12, #e67e22);
    border: 2px solid #f1c40f;
    box-shadow: 0 0 20px rgba(241, 196, 64, 0.8);
    animation: explosionFlash 0.3s ease-out;
}

@keyframes explosionFlash {
    0% {
        transform: scale(0.5);
        opacity: 1;
        background: radial-gradient(circle, #fff, #f39c12);
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

@keyframes playerFlash {
    0% { opacity: 0.3; }
    100% { opacity: 0.8; }
}

/* UI Styles */
.game-ui {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 100;
    color: white;
    font-family: 'EngraversGothic', sans-serif;
}

.player-stats {
    background: rgba(0,0,0,0.8);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    border: 2px solid #34495e;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.lives-icon { background: #e74c3c; }
.bombs-icon { background: #2c3e50; }
.flames-icon { background: #f39c12; }
.speed-icon { background: #2ecc71; }

.game-timer {
    background: rgba(0,0,0,0.8);
    padding: 10px 15px;
    border-radius: 10px;
    text-align: center;
    border: 2px solid #34495e;
    font-size: 18px;
    font-weight: bold;
}

@media screen and (max-width: 900px) {
    .splash-screen .menu{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 30px;
    }
    .splash-screen .logo-container img{
        width: 90vw;
        height: auto;
        animation: fadeIn 2s ease-in-out;
    }

    .game-map {
        transform: translate(-50%, -50%) scale(0.8);
    }
}