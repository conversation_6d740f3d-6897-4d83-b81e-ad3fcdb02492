<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman - Simple Version</title>
    <link rel="stylesheet" href="./static/style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #07131F;
            color: white;
            font-family: 'EngraversGothic', sans-serif;
        }
        
        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        .scene {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .scene.active {
            display: flex;
        }
        
        .nickname-scene {
            background: #07131F;
        }
        
        .nickname-form {
            background: #2c3e50;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .nickname-form h1 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 5px;
        }
        
        .nickname-input {
            padding: 15px 20px;
            font-size: 1.2rem;
            border: 2px solid #34495e;
            border-radius: 5px;
            background: #34495e;
            color: white;
            text-align: center;
            min-width: 300px;
            margin-bottom: 20px;
        }
        
        .nickname-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }
        
        .game-button {
            padding: 15px 30px;
            font-size: 1.1rem;
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            margin: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'EngraversGothic', sans-serif;
            transition: all 0.3s ease;
        }
        
        .game-button.primary {
            background: #3498db;
        }
        
        .game-button.secondary {
            background: #e74c3c;
        }
        
        .game-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .lobby-scene {
            background: #07131F;
        }
        
        .lobby-container {
            background: #2c3e50;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            min-width: 500px;
        }
        
        .lobby-title {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #3498db;
        }
        
        .player-info {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: white;
        }
        
        .status-message {
            font-size: 1rem;
            color: #f39c12;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Nickname Scene -->
        <div id="nickname-scene" class="scene nickname-scene active">
            <div class="nickname-form">
                <h1>Enter Your Nickname</h1>
                <input type="text" id="nickname-input" class="nickname-input" placeholder="Enter nickname..." maxlength="20">
                <br>
                <button id="join-button" class="game-button primary">Join Game</button>
                <button id="back-button" class="game-button secondary">Back to Main Menu</button>
            </div>
        </div>
        
        <!-- Lobby Scene -->
        <div id="lobby-scene" class="scene lobby-scene">
            <div class="lobby-container">
                <h1 class="lobby-title">Waiting for Players</h1>
                <div id="player-info" class="player-info">Player: <span id="player-name"></span></div>
                <div id="status-message" class="status-message">Connecting to game server...</div>
                <button id="start-game-button" class="game-button primary" style="display: none;">Start Game</button>
                <button id="leave-lobby-button" class="game-button secondary">Leave Lobby</button>
            </div>
        </div>
    </div>

    <script>
        // Simple scene management
        let currentScene = 'nickname';
        let playerNickname = '';
        
        function showScene(sceneName) {
            // Hide all scenes
            document.querySelectorAll('.scene').forEach(scene => {
                scene.classList.remove('active');
            });
            
            // Show target scene
            const targetScene = document.getElementById(sceneName + '-scene');
            if (targetScene) {
                targetScene.classList.add('active');
                currentScene = sceneName;
            }
        }
        
        function initializeGame() {
            console.log('Initializing simple game...');
            
            // Get elements
            const nicknameInput = document.getElementById('nickname-input');
            const joinButton = document.getElementById('join-button');
            const backButton = document.getElementById('back-button');
            const playerNameSpan = document.getElementById('player-name');
            const statusMessage = document.getElementById('status-message');
            const startGameButton = document.getElementById('start-game-button');
            const leaveLobbyButton = document.getElementById('leave-lobby-button');
            
            // Focus nickname input
            nicknameInput.focus();
            
            // Nickname form handlers
            joinButton.addEventListener('click', function() {
                const nickname = nicknameInput.value.trim();
                if (nickname.length >= 2) {
                    playerNickname = nickname;
                    playerNameSpan.textContent = nickname;
                    showScene('lobby');
                    
                    // Simulate lobby logic
                    setTimeout(() => {
                        statusMessage.textContent = 'Ready to start! (This is a demo - full multiplayer requires server)';
                        startGameButton.style.display = 'inline-block';
                    }, 2000);
                } else {
                    alert('Nickname must be at least 2 characters long');
                }
            });
            
            backButton.addEventListener('click', function() {
                window.location.href = '/';
            });
            
            // Lobby handlers
            startGameButton.addEventListener('click', function() {
                alert('Game would start here!\n\nThis is a simplified demo.\nThe full game requires the complete framework and multiplayer server.');
            });
            
            leaveLobbyButton.addEventListener('click', function() {
                showScene('nickname');
                nicknameInput.value = '';
                nicknameInput.focus();
            });
            
            // Enter key support
            nicknameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    joinButton.click();
                }
            });
            
            console.log('Simple game initialized successfully!');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeGame);
        
        // Prevent default behavior for arrow keys
        document.addEventListener('keydown', function(e) {
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(e.code)) {
                e.preventDefault();
            }
        });
        
        console.log('Simple game script loaded');
    </script>
</body>
</html>
