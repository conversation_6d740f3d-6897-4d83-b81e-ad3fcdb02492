/**
 * Simple WebSocket Server for Bomberman Multiplayer
 * Run with: node server.js
 */

const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');

// Create HTTP server for serving static files
const server = http.createServer((req, res) => {
    // Simple static file server
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }
    
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };
    
    const contentType = mimeTypes[extname] || 'application/octet-stream';
    
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>', 'utf-8');
            } else {
                res.writeHead(500);
                res.end('Server Error: ' + error.code + ' ..\n');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Game state
const rooms = new Map();
const players = new Map();

class GameRoom {
    constructor(roomCode) {
        this.roomCode = roomCode;
        this.players = new Map();
        this.gameState = 'waiting'; // waiting, countdown, playing, finished
        this.maxPlayers = 4;
        this.countdownTimer = null;
        this.gameStartTime = null;
    }
    
    addPlayer(playerId, playerData) {
        this.players.set(playerId, playerData);
        this.broadcastToRoom('playerJoined', playerData);
        
        // Check if we should start countdown
        if (this.players.size >= 2 && this.gameState === 'waiting') {
            this.startCountdown();
        }
    }
    
    removePlayer(playerId) {
        const player = this.players.get(playerId);
        if (player) {
            this.players.delete(playerId);
            this.broadcastToRoom('playerLeft', { playerId });
            
            // Cancel countdown if not enough players
            if (this.players.size < 2 && this.countdownTimer) {
                clearTimeout(this.countdownTimer);
                this.countdownTimer = null;
                this.gameState = 'waiting';
            }
        }
    }
    
    startCountdown() {
        if (this.gameState !== 'waiting') return;
        
        this.gameState = 'countdown';
        
        // 10 second countdown
        this.countdownTimer = setTimeout(() => {
            this.startGame();
        }, 10000);
        
        this.broadcastToRoom('gameCountdown', { countdown: 10 });
    }
    
    startGame() {
        this.gameState = 'playing';
        this.gameStartTime = Date.now();
        this.broadcastToRoom('gameStart', {
            players: Array.from(this.players.values()),
            startTime: this.gameStartTime
        });
    }
    
    broadcastToRoom(type, data) {
        const message = JSON.stringify({ type, data });
        this.players.forEach(player => {
            if (player.ws && player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(message);
            }
        });
    }
    
    broadcastToOthers(senderId, type, data) {
        const message = JSON.stringify({ type, data });
        this.players.forEach((player, playerId) => {
            if (playerId !== senderId && player.ws && player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(message);
            }
        });
    }
}

// WebSocket connection handling
wss.on('connection', (ws) => {
    console.log('New WebSocket connection');
    
    let playerId = null;
    let currentRoom = null;
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleMessage(ws, data);
        } catch (error) {
            console.error('Error parsing message:', error);
            ws.send(JSON.stringify({ type: 'error', data: { message: 'Invalid message format' } }));
        }
    });
    
    ws.on('close', () => {
        console.log('WebSocket connection closed');
        if (playerId && currentRoom) {
            currentRoom.removePlayer(playerId);
            players.delete(playerId);
            
            // Remove empty rooms
            if (currentRoom.players.size === 0) {
                rooms.delete(currentRoom.roomCode);
            }
        }
    });
    
    function handleMessage(ws, message) {
        const { type, data } = message;
        
        switch (type) {
            case 'setNickname':
                playerId = generatePlayerId();
                players.set(playerId, {
                    id: playerId,
                    nickname: data.nickname,
                    ws: ws
                });
                ws.send(JSON.stringify({ 
                    type: 'nicknameSet', 
                    data: { playerId } 
                }));
                break;
                
            case 'createRoom':
                if (!playerId) return;

                const roomCode = generateRoomCode();
                const room = new GameRoom(roomCode);
                rooms.set(roomCode, room);
                currentRoom = room;

                const createPlayerData = players.get(playerId);
                room.addPlayer(playerId, createPlayerData);

                ws.send(JSON.stringify({
                    type: 'roomCreated',
                    data: { roomCode, playerId }
                }));
                break;

            case 'joinRoom':
                if (!playerId || !data.roomCode) return;

                const targetRoom = rooms.get(data.roomCode);
                if (!targetRoom) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        data: { message: 'Room not found' }
                    }));
                    return;
                }

                if (targetRoom.players.size >= targetRoom.maxPlayers) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        data: { message: 'Room is full' }
                    }));
                    return;
                }

                currentRoom = targetRoom;
                const joinPlayerData = players.get(playerId);
                targetRoom.addPlayer(playerId, joinPlayerData);
                
                ws.send(JSON.stringify({ 
                    type: 'roomJoined', 
                    data: { 
                        roomCode: data.roomCode,
                        players: Array.from(targetRoom.players.values())
                    } 
                }));
                break;
                
            case 'leaveRoom':
                if (currentRoom && playerId) {
                    currentRoom.removePlayer(playerId);
                    currentRoom = null;
                }
                break;
                
            case 'chatMessage':
                if (currentRoom && playerId) {
                    currentRoom.broadcastToRoom('chatMessage', {
                        playerId,
                        nickname: data.nickname,
                        message: data.message,
                        timestamp: data.timestamp
                    });
                }
                break;
                
            case 'playerMove':
                if (currentRoom && playerId) {
                    currentRoom.broadcastToOthers(playerId, 'playerMove', {
                        playerId,
                        x: data.x,
                        y: data.y,
                        direction: data.direction
                    });
                }
                break;
                
            case 'bombPlace':
                if (currentRoom && playerId) {
                    currentRoom.broadcastToOthers(playerId, 'bombPlaced', {
                        playerId,
                        x: data.x,
                        y: data.y
                    });
                }
                break;
                
            default:
                console.log('Unknown message type:', type);
        }
    }
});

function generatePlayerId() {
    return 'player_' + Math.random().toString(36).substr(2, 9);
}

function generateRoomCode() {
    return Math.random().toString(36).substr(2, 6).toUpperCase();
}

// Start server
const PORT = process.env.PORT || 8081;
server.listen(PORT, () => {
    console.log(`Bomberman server running on port ${PORT}`);
    console.log(`Game available at: http://localhost:${PORT}`);
    console.log(`WebSocket server ready for connections`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    wss.close(() => {
        server.close(() => {
            console.log('Server closed');
            process.exit(0);
        });
    });
});
