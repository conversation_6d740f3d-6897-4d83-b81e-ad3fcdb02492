<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman Multiplayer - Game Overview</title>
    <link rel="stylesheet" href="./static/style.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'EngraversGothic', 'Arial', sans-serif;
            background: linear-gradient(135deg, #07131F 0%, #2c3e50 50%, #34495e 100%);
            color: white;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(7, 19, 31, 0.95);
            padding: 20px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #3498db;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-size: 1.1rem;
            transition: color 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-links a:hover {
            color: #3498db;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: 
                radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(231, 76, 60, 0.1) 0%, transparent 50%);
            position: relative;
        }

        .hero-content h1 {
            font-size: 4rem;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 5px;
            background: linear-gradient(45deg, #3498db, #e74c3c, #f39c12);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(52, 152, 219, 0.5); }
            to { text-shadow: 0 0 30px rgba(231, 76, 60, 0.8); }
        }

        .hero-content p {
            font-size: 1.5rem;
            margin-bottom: 40px;
            color: #bdc3c7;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            font-size: 1.2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: inherit;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
        }

        .btn-secondary {
            background: transparent;
            color: #3498db;
            border: 2px solid #3498db;
        }

        .btn-secondary:hover {
            background: #3498db;
            color: white;
            transform: translateY(-3px);
        }

        /* Sections */
        .section {
            padding: 80px 0;
        }

        .section-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 60px;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #3498db;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(52, 73, 94, 0.8);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            border-color: #3498db;
            transform: translateY(-10px);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
        }

        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #3498db;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        /* Game Flow */
        .game-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .flow-step {
            background: rgba(44, 62, 80, 0.9);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            border-left: 4px solid #3498db;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 20px;
            font-size: 1.2rem;
        }

        /* Technical Specs */
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .tech-card {
            background: rgba(44, 62, 80, 0.8);
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #34495e;
        }

        .tech-card h4 {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 1.3rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .tech-list {
            list-style: none;
        }

        .tech-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(52, 152, 219, 0.2);
        }

        .tech-list li:before {
            content: "▶ ";
            color: #3498db;
            font-weight: bold;
        }

        /* Controls Section */
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .control-item {
            background: rgba(52, 73, 94, 0.6);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #34495e;
        }

        .control-key {
            background: #3498db;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
            font-family: monospace;
        }

        /* Footer */
        .footer {
            background: rgba(7, 19, 31, 0.95);
            padding: 40px 0;
            text-align: center;
            border-top: 2px solid #3498db;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }

        /* Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Scroll animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="logo">💣 Bomberman</div>
            <div class="nav-links">
                <a href="#features">Features</a>
                <a href="#gameplay">Gameplay</a>
                <a href="#technical">Technical</a>
                <a href="#setup">Setup</a>
                <a href="/">Play Now</a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content fade-in">
            <h1>Bomberman Multiplayer</h1>
            <p>Experience the classic arcade action with modern multiplayer technology</p>
            <div class="cta-buttons">
                <a href="/" class="btn btn-primary">🎮 Play Now</a>
                <a href="#features" class="btn btn-secondary">📖 Learn More</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Game Features</h2>
            <div class="features-grid">
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">👥</span>
                    <h3>Multiplayer Action</h3>
                    <p>Battle with 2-4 players in real-time. Experience the thrill of competitive Bomberman with friends from around the world.</p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">💣</span>
                    <h3>Classic Gameplay</h3>
                    <p>Place bombs, destroy blocks, and collect power-ups. All the classic Bomberman mechanics you know and love.</p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">⚡</span>
                    <h3>60 FPS Performance</h3>
                    <p>Built with a custom DOM-based framework optimized for smooth 60 FPS gameplay with zero frame drops.</p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">🌐</span>
                    <h3>Real-time Chat</h3>
                    <p>Communicate with other players using the built-in WebSocket-powered chat system during lobby and gameplay.</p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">🎯</span>
                    <h3>Power-ups</h3>
                    <p>Collect bombs, flames, and speed power-ups to gain tactical advantages over your opponents.</p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <span class="feature-icon">🏆</span>
                    <h3>Competitive</h3>
                    <p>Each player starts with 3 lives. Be the last one standing to claim victory in intense multiplayer battles.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Game Preview Section -->
    <section class="section" style="background: rgba(7, 19, 31, 0.8);">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Game Preview</h2>
            <div style="max-width: 900px; margin: 0 auto; text-align: center;">
                <!-- Game Interface Mockup -->
                <div style="background: #2c3e50; border: 3px solid #3498db; border-radius: 15px; padding: 20px; margin-bottom: 40px; position: relative; overflow: hidden;">
                    <div style="background: #34495e; padding: 15px; border-radius: 10px; margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: #3498db; font-weight: bold;">💣 Bomberman Multiplayer</div>
                        <div style="color: #e74c3c; font-weight: bold;">Players: 3/4</div>
                    </div>

                    <!-- Game Map Mockup -->
                    <div style="background: #27ae60; border: 2px solid #2ecc71; border-radius: 8px; padding: 20px; margin: 20px 0; position: relative; min-height: 300px; display: grid; grid-template-columns: repeat(13, 1fr); grid-template-rows: repeat(11, 1fr); gap: 2px;">
                        <!-- Walls and blocks pattern -->
                        <div style="background: #7f8c8d; border-radius: 3px;"></div>
                        <div style="background: #7f8c8d; border-radius: 3px;"></div>
                        <div style="background: #7f8c8d; border-radius: 3px;"></div>
                        <div style="background: #d35400; border-radius: 3px;"></div>
                        <div style="background: #d35400; border-radius: 3px;"></div>

                        <!-- Player positions -->
                        <div style="background: #3498db; border-radius: 50%; position: relative; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">P1</div>
                        <div style="background: #e74c3c; border-radius: 50%; position: relative; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">P2</div>

                        <!-- Bombs -->
                        <div style="background: #2c3e50; border: 2px solid #f39c12; border-radius: 50%; position: relative; display: flex; align-items: center; justify-content: center; color: #f39c12; font-weight: bold;">💣</div>

                        <!-- Power-ups -->
                        <div style="background: #9b59b6; border-radius: 3px; display: flex; align-items: center; justify-content: center; color: white;">⚡</div>
                        <div style="background: #e67e22; border-radius: 3px; display: flex; align-items: center; justify-content: center; color: white;">🔥</div>
                    </div>

                    <!-- Game UI Mockup -->
                    <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(52, 73, 94, 0.8); padding: 15px; border-radius: 8px;">
                        <div style="color: #3498db;">
                            <div style="font-size: 0.9rem; margin-bottom: 5px;">Player 1</div>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <span style="color: #e74c3c;">❤️ 3</span>
                                <span style="color: #f39c12;">💣 2</span>
                                <span style="color: #2ecc71;">🔥 3</span>
                            </div>
                        </div>
                        <div style="color: #3498db; font-size: 1.2rem; font-weight: bold;">
                            Time: 2:45
                        </div>
                        <div style="color: #e74c3c;">
                            <div style="font-size: 0.9rem; margin-bottom: 5px;">Player 2</div>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <span style="color: #e74c3c;">❤️ 2</span>
                                <span style="color: #f39c12;">💣 1</span>
                                <span style="color: #2ecc71;">🔥 2</span>
                            </div>
                        </div>
                    </div>
                </div>

                <p style="font-size: 1.2rem; color: #bdc3c7; margin-bottom: 30px;">
                    Experience intense multiplayer battles with real-time synchronization, strategic bomb placement, and competitive power-up collection!
                </p>

                <div class="cta-buttons">
                    <a href="/" class="btn btn-primary">🎮 Play Now</a>
                    <a href="/test-full-game.html" class="btn btn-secondary">🧪 Test Game</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Gameplay Section -->
    <section id="gameplay" class="section" style="background: rgba(44, 62, 80, 0.3);">
        <div class="container">
            <h2 class="section-title animate-on-scroll">How to Play</h2>
            <div class="game-flow">
                <div class="flow-step animate-on-scroll">
                    <div class="step-number">1</div>
                    <h3>Enter Nickname</h3>
                    <p>Choose your unique player name to identify yourself in the multiplayer arena.</p>
                </div>
                <div class="flow-step animate-on-scroll">
                    <div class="step-number">2</div>
                    <h3>Join Lobby</h3>
                    <p>Wait for other players to join. Chat with opponents while waiting for the game to start.</p>
                </div>
                <div class="flow-step animate-on-scroll">
                    <div class="step-number">3</div>
                    <h3>Game Starts</h3>
                    <p>With 4 players or after 20 seconds with 2+ players, a 10-second countdown begins.</p>
                </div>
                <div class="flow-step animate-on-scroll">
                    <div class="step-number">4</div>
                    <h3>Battle!</h3>
                    <p>Use bombs strategically to eliminate opponents and be the last player standing.</p>
                </div>
            </div>

            <div style="margin-top: 60px;">
                <h3 style="text-align: center; color: #3498db; margin-bottom: 30px; font-size: 2rem;">Game Controls</h3>
                <div class="controls-grid">
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">↑ ↓ ← →</div>
                        <p>Move Player</p>
                    </div>
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">WASD</div>
                        <p>Alternative Movement</p>
                    </div>
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">SPACE</div>
                        <p>Place Bomb</p>
                    </div>
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">F1</div>
                        <p>Performance Display</p>
                    </div>
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">ENTER</div>
                        <p>Send Chat Message</p>
                    </div>
                    <div class="control-item animate-on-scroll">
                        <div class="control-key">H</div>
                        <p>Toggle Help</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Section -->
    <section id="technical" class="section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Technical Excellence</h2>
            <div class="tech-grid">
                <div class="tech-card animate-on-scroll">
                    <h4>🎮 Custom Game Framework</h4>
                    <ul class="tech-list">
                        <li>Entity-Component-System Architecture</li>
                        <li>Optimized DOM Rendering System</li>
                        <li>Physics & Collision Detection</li>
                        <li>Input & Audio Systems</li>
                        <li>Performance Monitoring</li>
                    </ul>
                </div>
                <div class="tech-card animate-on-scroll">
                    <h4>⚡ Performance Features</h4>
                    <ul class="tech-list">
                        <li>60 FPS Guaranteed</li>
                        <li>Zero Frame Drops</li>
                        <li>RequestAnimationFrame Optimization</li>
                        <li>Minimal DOM Manipulation</li>
                        <li>Memory Efficient Design</li>
                    </ul>
                </div>
                <div class="tech-card animate-on-scroll">
                    <h4>🌐 Multiplayer Technology</h4>
                    <ul class="tech-list">
                        <li>WebSocket Real-time Communication</li>
                        <li>Player Synchronization</li>
                        <li>Room Management System</li>
                        <li>Graceful Disconnection Handling</li>
                        <li>Chat System Integration</li>
                    </ul>
                </div>
                <div class="tech-card animate-on-scroll">
                    <h4>🎯 Game Systems</h4>
                    <ul class="tech-list">
                        <li>Procedural Map Generation</li>
                        <li>Bomb & Explosion Mechanics</li>
                        <li>Power-up Collection System</li>
                        <li>Player Life Management</li>
                        <li>Victory Condition Detection</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 60px; text-align: center;">
                <h3 style="color: #3498db; margin-bottom: 30px; font-size: 2rem;">Architecture Highlights</h3>
                <div style="background: rgba(52, 73, 94, 0.8); padding: 40px; border-radius: 15px; max-width: 800px; margin: 0 auto;">
                    <p style="font-size: 1.2rem; line-height: 1.8; color: #bdc3c7;">
                        Built entirely with a <strong style="color: #3498db;">custom DOM-based framework</strong> that follows modern game development patterns.
                        No Canvas, WebGL, or external game engines - just pure JavaScript performance optimization and
                        innovative DOM manipulation techniques that deliver console-quality gaming in the browser.
                    </p>
                </div>
            </div>

            <!-- Performance Showcase -->
            <div style="margin-top: 60px;">
                <h3 style="color: #3498db; margin-bottom: 30px; font-size: 2rem; text-align: center;">Performance Metrics</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="background: rgba(46, 204, 113, 0.2); border: 2px solid #2ecc71; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2.5rem; color: #2ecc71; font-weight: bold;">60</div>
                        <div style="color: #2ecc71; font-weight: bold;">FPS Guaranteed</div>
                    </div>
                    <div style="background: rgba(52, 152, 219, 0.2); border: 2px solid #3498db; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2.5rem; color: #3498db; font-weight: bold;">0</div>
                        <div style="color: #3498db; font-weight: bold;">Frame Drops</div>
                    </div>
                    <div style="background: rgba(243, 156, 18, 0.2); border: 2px solid #f39c12; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2.5rem; color: #f39c12; font-weight: bold;">&lt;16</div>
                        <div style="color: #f39c12; font-weight: bold;">ms Frame Time</div>
                    </div>
                    <div style="background: rgba(155, 89, 182, 0.2); border: 2px solid #9b59b6; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2.5rem; color: #9b59b6; font-weight: bold;">4</div>
                        <div style="color: #9b59b6; font-weight: bold;">Players Max</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Setup Section -->
    <section id="setup" class="section" style="background: rgba(44, 62, 80, 0.3);">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Quick Setup Guide</h2>
            <div style="max-width: 800px; margin: 0 auto;">
                <div style="background: rgba(7, 19, 31, 0.8); padding: 40px; border-radius: 15px; border-left: 4px solid #3498db;">
                    <h3 style="color: #3498db; margin-bottom: 20px; font-size: 1.5rem;">Prerequisites</h3>
                    <ul style="list-style: none; margin-bottom: 30px;">
                        <li style="padding: 8px 0;"><span style="color: #3498db;">▶</span> Node.js (version 14 or higher)</li>
                        <li style="padding: 8px 0;"><span style="color: #3498db;">▶</span> Modern web browser (Chrome, Firefox, Safari, Edge)</li>
                    </ul>

                    <h3 style="color: #3498db; margin-bottom: 20px; font-size: 1.5rem;">Installation Steps</h3>
                    <div style="background: #2c3e50; padding: 20px; border-radius: 8px; margin-bottom: 20px; font-family: monospace;">
                        <div style="color: #f39c12; margin-bottom: 10px;"># 1. Install dependencies</div>
                        <div style="color: #2ecc71;">npm install</div>
                        <br>
                        <div style="color: #f39c12; margin-bottom: 10px;"># 2. Start the server</div>
                        <div style="color: #2ecc71;">npm start</div>
                        <br>
                        <div style="color: #f39c12; margin-bottom: 10px;"># 3. Open in browser</div>
                        <div style="color: #2ecc71;">http://localhost:8081</div>
                    </div>

                    <div style="background: rgba(52, 152, 219, 0.1); padding: 20px; border-radius: 8px; border: 1px solid #3498db;">
                        <h4 style="color: #3498db; margin-bottom: 10px;">💡 Pro Tip</h4>
                        <p>For multiplayer testing, open multiple browser tabs or use different browsers. The game supports 2-4 players simultaneously!</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Game Rules Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Game Rules & Strategy</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 40px;">
                <div style="background: rgba(52, 73, 94, 0.8); padding: 30px; border-radius: 15px;">
                    <h3 style="color: #e74c3c; margin-bottom: 20px; font-size: 1.5rem;">🎯 Objective</h3>
                    <p style="font-size: 1.1rem; line-height: 1.6;">Be the last player standing by strategically placing bombs to eliminate opponents while avoiding explosions yourself.</p>
                </div>
                <div style="background: rgba(52, 73, 94, 0.8); padding: 30px; border-radius: 15px;">
                    <h3 style="color: #f39c12; margin-bottom: 20px; font-size: 1.5rem;">💣 Power-ups</h3>
                    <ul style="list-style: none; font-size: 1.1rem;">
                        <li style="padding: 5px 0;"><span style="color: #e74c3c;">💣</span> <strong>Bombs:</strong> +1 max bombs</li>
                        <li style="padding: 5px 0;"><span style="color: #f39c12;">🔥</span> <strong>Flames:</strong> +1 explosion range</li>
                        <li style="padding: 5px 0;"><span style="color: #2ecc71;">⚡</span> <strong>Speed:</strong> Faster movement</li>
                    </ul>
                </div>
                <div style="background: rgba(52, 73, 94, 0.8); padding: 30px; border-radius: 15px;">
                    <h3 style="color: #2ecc71; margin-bottom: 20px; font-size: 1.5rem;">🛡️ Lives System</h3>
                    <p style="font-size: 1.1rem; line-height: 1.6;">Each player starts with 3 lives. Getting caught in an explosion costs 1 life. Lose all lives and you're eliminated!</p>
                </div>
                <div style="background: rgba(52, 73, 94, 0.8); padding: 30px; border-radius: 15px;">
                    <h3 style="color: #9b59b6; margin-bottom: 20px; font-size: 1.5rem;">🗺️ Map Elements</h3>
                    <ul style="list-style: none; font-size: 1.1rem;">
                        <li style="padding: 5px 0;"><span style="color: #7f8c8d;">⬜</span> <strong>Walls:</strong> Indestructible</li>
                        <li style="padding: 5px 0;"><span style="color: #d35400;">🟫</span> <strong>Blocks:</strong> Destructible</li>
                        <li style="padding: 5px 0;"><span style="color: #3498db;">🔵</span> <strong>Spawn:</strong> Safe zones</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
                <div>
                    <h4 style="color: #3498db; margin-bottom: 15px;">🎮 Quick Links</h4>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <a href="/" style="color: #bdc3c7; text-decoration: none;">Play Game</a>
                        <a href="/test-framework.html" style="color: #bdc3c7; text-decoration: none;">Framework Test</a>
                        <a href="/test-full-game.html" style="color: #bdc3c7; text-decoration: none;">Full Game Test</a>
                    </div>
                </div>
                <div>
                    <h4 style="color: #3498db; margin-bottom: 15px;">⚡ Performance</h4>
                    <p style="color: #bdc3c7; font-size: 0.9rem;">Built for 60 FPS gameplay with zero frame drops. Press F1 in-game to monitor performance metrics.</p>
                </div>
                <div>
                    <h4 style="color: #3498db; margin-bottom: 15px;">🌐 Multiplayer</h4>
                    <p style="color: #bdc3c7; font-size: 0.9rem;">Real-time WebSocket communication enables seamless multiplayer battles with up to 4 players.</p>
                </div>
            </div>
            <div style="border-top: 1px solid #34495e; padding-top: 20px; text-align: center;">
                <p style="color: #7f8c8d;">&copy; 2024 Bomberman Multiplayer - Built with Custom DOM Framework</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-10px) scale(1)';
            });
        });
    </script>
</body>
</html>
