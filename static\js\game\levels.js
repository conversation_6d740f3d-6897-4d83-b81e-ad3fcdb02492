/**
 * Level Configurations for Bomberman
 */

class GameLevels {
    constructor() {
        this.levels = new Map();
        this.initializeLevels();
    }
    
    initializeLevels() {
        // Level 1: Classic Arena
        this.levels.set('classic', {
            id: 'classic',
            name: 'Classic Arena',
            description: 'The traditional Bomberman battlefield with balanced block placement',
            difficulty: 'Easy',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.6,
            powerUpChance: 0.3,
            theme: 'classic',
            preview: this.generatePreview('classic'),
            spawnPositions: [
                { x: 1, y: 1 },
                { x: 13, y: 1 },
                { x: 1, y: 11 },
                { x: 13, y: 11 }
            ],
            specialFeatures: []
        });
        
        // Level 2: Maze Runner
        this.levels.set('maze', {
            id: 'maze',
            name: 'Maze Runner',
            description: 'Navigate through narrow corridors and tight spaces',
            difficulty: 'Medium',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.8,
            powerUpChance: 0.25,
            theme: 'maze',
            preview: this.generatePreview('maze'),
            spawnPositions: [
                { x: 1, y: 1 },
                { x: 13, y: 1 },
                { x: 1, y: 11 },
                { x: 13, y: 11 }
            ],
            specialFeatures: ['narrow_corridors']
        });
        
        // Level 3: Open Field
        this.levels.set('open', {
            id: 'open',
            name: 'Open Field',
            description: 'Wide open spaces with minimal cover - fast-paced action',
            difficulty: 'Hard',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.3,
            powerUpChance: 0.4,
            theme: 'open',
            preview: this.generatePreview('open'),
            spawnPositions: [
                { x: 1, y: 1 },
                { x: 13, y: 1 },
                { x: 1, y: 11 },
                { x: 13, y: 11 }
            ],
            specialFeatures: ['high_visibility', 'fast_paced']
        });
        
        // Level 4: Fortress
        this.levels.set('fortress', {
            id: 'fortress',
            name: 'Fortress Siege',
            description: 'Defensive positions with strategic chokepoints',
            difficulty: 'Expert',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.7,
            powerUpChance: 0.2,
            theme: 'fortress',
            preview: this.generatePreview('fortress'),
            spawnPositions: [
                { x: 2, y: 2 },
                { x: 12, y: 2 },
                { x: 2, y: 10 },
                { x: 12, y: 10 }
            ],
            specialFeatures: ['defensive_positions', 'chokepoints']
        });
        
        // Level 5: Cross Roads
        this.levels.set('cross', {
            id: 'cross',
            name: 'Cross Roads',
            description: 'Central meeting point with four distinct quadrants',
            difficulty: 'Medium',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.5,
            powerUpChance: 0.35,
            theme: 'cross',
            preview: this.generatePreview('cross'),
            spawnPositions: [
                { x: 1, y: 1 },
                { x: 13, y: 1 },
                { x: 1, y: 11 },
                { x: 13, y: 11 }
            ],
            specialFeatures: ['central_hub', 'quadrants']
        });
        
        // Level 6: Random Chaos
        this.levels.set('random', {
            id: 'random',
            name: 'Random Chaos',
            description: 'Completely randomized layout - no two games are the same',
            difficulty: 'Variable',
            mapWidth: 15,
            mapHeight: 13,
            blockDensity: 0.65,
            powerUpChance: 0.3,
            theme: 'random',
            preview: this.generatePreview('random'),
            spawnPositions: [
                { x: 1, y: 1 },
                { x: 13, y: 1 },
                { x: 1, y: 11 },
                { x: 13, y: 11 }
            ],
            specialFeatures: ['randomized', 'unpredictable']
        });
    }
    
    generatePreview(levelType) {
        // Generate a simple ASCII preview of the level layout
        const width = 15;
        const height = 13;
        let preview = [];
        
        // Initialize with empty spaces
        for (let y = 0; y < height; y++) {
            preview[y] = [];
            for (let x = 0; x < width; x++) {
                preview[y][x] = '·';
            }
        }
        
        // Add borders
        for (let x = 0; x < width; x++) {
            preview[0][x] = '█';
            preview[height - 1][x] = '█';
        }
        for (let y = 0; y < height; y++) {
            preview[y][0] = '█';
            preview[y][width - 1] = '█';
        }
        
        // Add internal walls (every other cell in grid pattern)
        for (let y = 2; y < height - 1; y += 2) {
            for (let x = 2; x < width - 1; x += 2) {
                preview[y][x] = '█';
            }
        }
        
        // Add spawn points
        preview[1][1] = '1';
        preview[1][width - 2] = '2';
        preview[height - 2][1] = '3';
        preview[height - 2][width - 2] = '4';
        
        // Add level-specific patterns
        switch (levelType) {
            case 'maze':
                this.addMazePattern(preview, width, height);
                break;
            case 'open':
                this.addOpenPattern(preview, width, height);
                break;
            case 'fortress':
                this.addFortressPattern(preview, width, height);
                break;
            case 'cross':
                this.addCrossPattern(preview, width, height);
                break;
            case 'random':
                this.addRandomPattern(preview, width, height);
                break;
            default:
                this.addClassicPattern(preview, width, height);
        }
        
        return preview;
    }
    
    addClassicPattern(preview, width, height) {
        // Add blocks in a balanced pattern
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.6) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    addMazePattern(preview, width, height) {
        // Add more blocks to create maze-like corridors
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.8) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    addOpenPattern(preview, width, height) {
        // Add fewer blocks for open gameplay
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.3) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    addFortressPattern(preview, width, height) {
        // Add defensive structures
        const centerX = Math.floor(width / 2);
        const centerY = Math.floor(height / 2);
        
        // Central fortress
        for (let y = centerY - 1; y <= centerY + 1; y++) {
            for (let x = centerX - 1; x <= centerX + 1; x++) {
                if (x >= 0 && x < width && y >= 0 && y < height) {
                    if (x === centerX && y === centerY) {
                        preview[y][x] = '·'; // Keep center open
                    } else {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
        
        // Add other blocks
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.7) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    addCrossPattern(preview, width, height) {
        // Create cross-shaped open areas
        const centerX = Math.floor(width / 2);
        const centerY = Math.floor(height / 2);
        
        // Horizontal corridor
        for (let x = 3; x < width - 3; x++) {
            preview[centerY][x] = '·';
        }
        
        // Vertical corridor
        for (let y = 3; y < height - 3; y++) {
            preview[y][centerX] = '·';
        }
        
        // Add blocks in quadrants
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.5) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    addRandomPattern(preview, width, height) {
        // Completely random block placement
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (preview[y][x] === '·' && !this.isNearSpawn(x, y, width, height)) {
                    if (Math.random() < 0.65) {
                        preview[y][x] = '▓';
                    }
                }
            }
        }
    }
    
    isNearSpawn(x, y, width, height) {
        const spawnPositions = [
            { x: 1, y: 1 },
            { x: width - 2, y: 1 },
            { x: 1, y: height - 2 },
            { x: width - 2, y: height - 2 }
        ];
        
        return spawnPositions.some(spawn => {
            const distance = Math.abs(x - spawn.x) + Math.abs(y - spawn.y);
            return distance <= 2;
        });
    }
    
    getLevel(levelId) {
        return this.levels.get(levelId);
    }
    
    getAllLevels() {
        return Array.from(this.levels.values());
    }
    
    getLevelIds() {
        return Array.from(this.levels.keys());
    }
    
    getRandomLevel() {
        const levelIds = this.getLevelIds();
        const randomId = levelIds[Math.floor(Math.random() * levelIds.length)];
        return this.getLevel(randomId);
    }
}

// Export for use in other files
window.GameLevels = GameLevels;
