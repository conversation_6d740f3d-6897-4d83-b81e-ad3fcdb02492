/**
 * Mini-Framework Core
 * A lightweight DOM-based game framework for high-performance games
 */

class GameFramework {
    constructor() {
        this.components = new Map();
        this.entities = new Map();
        this.systems = [];
        this.isRunning = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fps = 0;
        this.frameCount = 0;
        this.fpsUpdateTime = 0;
        
        // Performance monitoring
        this.performanceMetrics = {
            frameTime: 0,
            updateTime: 0,
            renderTime: 0
        };
        
        // Event system
        this.eventListeners = new Map();
        
        // Scene management
        this.currentScene = null;
        this.scenes = new Map();
        
        this.init();
    }
    
    init() {
        // Bind the game loop
        this.gameLoop = this.gameLoop.bind(this);
        
        // Setup performance monitoring
        this.setupPerformanceMonitoring();
        
        // Setup global event listeners
        this.setupEventListeners();
    }
    
    setupPerformanceMonitoring() {
        // Create performance display element
        const perfDisplay = document.createElement('div');
        perfDisplay.id = 'performance-display';
        perfDisplay.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            border-radius: 5px;
            display: none;
        `;
        document.body.appendChild(perfDisplay);
        
        // Toggle performance display with F1
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') {
                e.preventDefault();
                const display = document.getElementById('performance-display');
                display.style.display = display.style.display === 'none' ? 'block' : 'none';
            }
        });
    }
    
    setupEventListeners() {
        // Prevent context menu
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // Handle visibility change for performance
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
    }
    
    // Entity Component System
    createEntity(id) {
        if (this.entities.has(id)) {
            console.warn(`Entity ${id} already exists`);
            return this.entities.get(id);
        }
        
        const entity = {
            id,
            components: new Map(),
            active: true
        };
        
        this.entities.set(id, entity);
        return entity;
    }
    
    removeEntity(id) {
        const entity = this.entities.get(id);
        if (entity) {
            // Remove all components
            entity.components.clear();
            this.entities.delete(id);
        }
    }
    
    addComponent(entityId, componentType, componentData) {
        const entity = this.entities.get(entityId);
        if (entity) {
            entity.components.set(componentType, componentData);
        }
    }
    
    getComponent(entityId, componentType) {
        const entity = this.entities.get(entityId);
        return entity ? entity.components.get(componentType) : null;
    }
    
    removeComponent(entityId, componentType) {
        const entity = this.entities.get(entityId);
        if (entity) {
            entity.components.delete(componentType);
        }
    }
    
    // System management
    addSystem(system) {
        this.systems.push(system);
        if (system.init) {
            system.init(this);
        }
    }

    getSystem(systemName) {
        return this.systems.find(system => system.name === systemName);
    }
    
    removeSystem(system) {
        const index = this.systems.indexOf(system);
        if (index > -1) {
            this.systems.splice(index, 1);
        }
    }
    
    // Scene management
    addScene(name, scene) {
        this.scenes.set(name, scene);
    }
    
    switchScene(name) {
        const scene = this.scenes.get(name);
        if (scene) {
            if (this.currentScene && this.currentScene.exit) {
                this.currentScene.exit();
            }
            
            this.currentScene = scene;
            
            if (scene.enter) {
                scene.enter();
            }
        }
    }
    
    // Game loop
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.lastTime = performance.now();
            requestAnimationFrame(this.gameLoop);
        }
    }
    
    pause() {
        this.isRunning = false;
    }
    
    resume() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.lastTime = performance.now();
            requestAnimationFrame(this.gameLoop);
        }
    }
    
    gameLoop(currentTime) {
        if (!this.isRunning) return;
        
        const frameStart = performance.now();
        
        // Calculate delta time
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent spiral of death
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        // Update FPS
        this.updateFPS(currentTime);
        
        // Update phase
        const updateStart = performance.now();
        this.update(this.deltaTime);
        this.performanceMetrics.updateTime = performance.now() - updateStart;
        
        // Render phase
        const renderStart = performance.now();
        this.render();
        this.performanceMetrics.renderTime = performance.now() - renderStart;
        
        // Calculate frame time
        this.performanceMetrics.frameTime = performance.now() - frameStart;
        
        // Update performance display
        this.updatePerformanceDisplay();
        
        // Continue loop
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        // Update current scene
        if (this.currentScene && this.currentScene.update) {
            this.currentScene.update(deltaTime);
        }
        
        // Update all systems
        for (const system of this.systems) {
            if (system.update) {
                system.update(deltaTime, this);
            }
        }
    }
    
    render() {
        // Render current scene
        if (this.currentScene && this.currentScene.render) {
            this.currentScene.render();
        }
        
        // Render all systems
        for (const system of this.systems) {
            if (system.render) {
                system.render(this);
            }
        }
    }
    
    updateFPS(currentTime) {
        this.frameCount++;
        if (currentTime - this.fpsUpdateTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.fpsUpdateTime = currentTime;
        }
    }
    
    updatePerformanceDisplay() {
        const display = document.getElementById('performance-display');
        if (display && display.style.display !== 'none') {
            display.innerHTML = `
                FPS: ${this.fps}<br>
                Frame Time: ${this.performanceMetrics.frameTime.toFixed(2)}ms<br>
                Update Time: ${this.performanceMetrics.updateTime.toFixed(2)}ms<br>
                Render Time: ${this.performanceMetrics.renderTime.toFixed(2)}ms<br>
                Entities: ${this.entities.size}<br>
                Systems: ${this.systems.length}
            `;
        }
    }
    
    // Event system
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    off(event, callback) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            for (const callback of listeners) {
                callback(data);
            }
        }
    }
}

// Export for use
window.GameFramework = GameFramework;
