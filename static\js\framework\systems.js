/**
 * System implementations for the game framework
 */

// Rendering system for DOM elements
class RenderSystem {
    constructor() {
        this.name = 'RenderSystem';
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    update(deltaTime, framework) {
        // Update all entities with Transform and DOMRenderer components
        for (const [entityId, entity] of framework.entities) {
            if (!entity.active) continue;
            
            const transform = entity.components.get('Transform');
            const renderer = entity.components.get('DOMRenderer');
            
            if (transform && renderer) {
                renderer.updateElement(transform);
            }
        }
    }
}

// Physics system for movement and collision
class PhysicsSystem {
    constructor() {
        this.name = 'PhysicsSystem';
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    update(deltaTime, framework) {
        // Update physics for all entities
        for (const [entityId, entity] of framework.entities) {
            if (!entity.active) continue;
            
            const transform = entity.components.get('Transform');
            const physics = entity.components.get('Physics');
            
            if (transform && physics) {
                // Update physics
                physics.update(deltaTime);
                
                // Apply velocity to position
                transform.translate(
                    physics.velocityX * deltaTime,
                    physics.velocityY * deltaTime
                );
            }
        }
    }
}

// Collision system for hit detection
class CollisionSystem {
    constructor() {
        this.name = 'CollisionSystem';
        this.collisionCallbacks = new Map();
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    onCollision(entityId, callback) {
        if (!this.collisionCallbacks.has(entityId)) {
            this.collisionCallbacks.set(entityId, []);
        }
        this.collisionCallbacks.get(entityId).push(callback);
    }
    
    removeCollisionCallbacks(entityId) {
        this.collisionCallbacks.delete(entityId);
    }
    
    update(deltaTime, framework) {
        const entities = Array.from(framework.entities.values()).filter(e => e.active);
        
        // Check collisions between all entities with colliders
        for (let i = 0; i < entities.length; i++) {
            for (let j = i + 1; j < entities.length; j++) {
                const entity1 = entities[i];
                const entity2 = entities[j];
                
                const transform1 = entity1.components.get('Transform');
                const collider1 = entity1.components.get('Collider');
                const transform2 = entity2.components.get('Transform');
                const collider2 = entity2.components.get('Collider');
                
                if (transform1 && collider1 && transform2 && collider2) {
                    if (collider1.intersects(collider2, transform1, transform2)) {
                        // Trigger collision callbacks
                        this.triggerCollision(entity1.id, entity2, collider2);
                        this.triggerCollision(entity2.id, entity1, collider1);
                    }
                }
            }
        }
    }
    
    triggerCollision(entityId, otherEntity, otherCollider) {
        const callbacks = this.collisionCallbacks.get(entityId);
        if (callbacks) {
            for (const callback of callbacks) {
                callback(otherEntity, otherCollider);
            }
        }
    }
    
    // Utility method to check collision between specific entities
    checkCollision(entityId1, entityId2) {
        const entity1 = this.framework.entities.get(entityId1);
        const entity2 = this.framework.entities.get(entityId2);
        
        if (!entity1 || !entity2) return false;
        
        const transform1 = entity1.components.get('Transform');
        const collider1 = entity1.components.get('Collider');
        const transform2 = entity2.components.get('Transform');
        const collider2 = entity2.components.get('Collider');
        
        if (transform1 && collider1 && transform2 && collider2) {
            return collider1.intersects(collider2, transform1, transform2);
        }
        
        return false;
    }
    
    // Get all entities colliding with a specific entity
    getCollisions(entityId) {
        const entity = this.framework.entities.get(entityId);
        if (!entity) return [];
        
        const transform = entity.components.get('Transform');
        const collider = entity.components.get('Collider');
        
        if (!transform || !collider) return [];
        
        const collisions = [];
        
        for (const [otherId, otherEntity] of this.framework.entities) {
            if (otherId === entityId || !otherEntity.active) continue;
            
            const otherTransform = otherEntity.components.get('Transform');
            const otherCollider = otherEntity.components.get('Collider');
            
            if (otherTransform && otherCollider) {
                if (collider.intersects(otherCollider, transform, otherTransform)) {
                    collisions.push(otherEntity);
                }
            }
        }
        
        return collisions;
    }
}

// Animation system for sprite animations
class AnimationSystem {
    constructor() {
        this.name = 'AnimationSystem';
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    update(deltaTime, framework) {
        for (const [entityId, entity] of framework.entities) {
            if (!entity.active) continue;
            
            const animation = entity.components.get('Animation');
            const renderer = entity.components.get('DOMRenderer');
            
            if (animation && renderer) {
                animation.update(deltaTime);
                
                // Update the DOM element with current frame
                const currentFrame = animation.getCurrentFrame();
                if (currentFrame && currentFrame.className) {
                    renderer.element.className = currentFrame.className;
                }
                if (currentFrame && currentFrame.style) {
                    Object.assign(renderer.element.style, currentFrame.style);
                }
            }
        }
    }
}

// Timer system for delayed actions
class TimerSystem {
    constructor() {
        this.name = 'TimerSystem';
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    update(deltaTime, framework) {
        for (const [entityId, entity] of framework.entities) {
            if (!entity.active) continue;
            
            const timer = entity.components.get('Timer');
            
            if (timer) {
                timer.update(deltaTime);
            }
        }
    }
}

// Input system for handling user input
class InputSystem {
    constructor() {
        this.name = 'InputSystem';
        this.inputHandlers = new Map();
    }
    
    init(framework) {
        this.framework = framework;
        this.input = new Components.Input();
    }
    
    addInputHandler(entityId, handler) {
        this.inputHandlers.set(entityId, handler);
    }
    
    removeInputHandler(entityId) {
        this.inputHandlers.delete(entityId);
    }
    
    update(deltaTime, framework) {
        // Process input for entities with input handlers
        for (const [entityId, handler] of this.inputHandlers) {
            const entity = framework.entities.get(entityId);
            if (entity && entity.active) {
                handler(this.input, entity, deltaTime);
            }
        }
    }
    
    isKeyPressed(keyCode) {
        return this.input.isKeyPressed(keyCode);
    }
    
    isMouseButtonPressed(button) {
        return this.input.isMouseButtonPressed(button);
    }
    
    getMousePosition() {
        return this.input.getMousePosition();
    }
}

// Audio system for sound effects and music
class AudioSystem {
    constructor() {
        this.name = 'AudioSystem';
        this.sounds = new Map();
        this.musicVolume = 0.5;
        this.sfxVolume = 0.7;
    }
    
    init(framework) {
        this.framework = framework;
    }
    
    loadSound(name, url, isMusic = false) {
        const audio = new Audio(url);
        audio.volume = isMusic ? this.musicVolume : this.sfxVolume;
        this.sounds.set(name, { audio, isMusic });
        return audio;
    }
    
    playSound(name, loop = false) {
        const sound = this.sounds.get(name);
        if (sound) {
            sound.audio.loop = loop;
            sound.audio.currentTime = 0;
            sound.audio.play().catch(e => console.warn('Audio play failed:', e));
        }
    }
    
    stopSound(name) {
        const sound = this.sounds.get(name);
        if (sound) {
            sound.audio.pause();
            sound.audio.currentTime = 0;
        }
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        for (const [name, sound] of this.sounds) {
            if (sound.isMusic) {
                sound.audio.volume = this.musicVolume;
            }
        }
    }
    
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        for (const [name, sound] of this.sounds) {
            if (!sound.isMusic) {
                sound.audio.volume = this.sfxVolume;
            }
        }
    }
}

// Export systems
window.Systems = {
    RenderSystem,
    PhysicsSystem,
    CollisionSystem,
    AnimationSystem,
    TimerSystem,
    InputSystem,
    AudioSystem
};
