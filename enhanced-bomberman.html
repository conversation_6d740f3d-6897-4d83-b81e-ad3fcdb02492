<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Enhanced Bomberman - Level Selection & Countdown</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: white;
            font-family: 'Courier New', monospace;
            overflow-x: hidden;
        }
        
        .screen {
            display: none;
            min-height: 100vh;
            padding: 20px;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            transition: all 0.5s ease;
        }
        
        .screen.active {
            display: flex;
        }
        
        /* Level Selection Screen */
        .level-selection {
            text-align: center;
            max-width: 1200px;
            width: 100%;
        }
        
        .title {
            font-size: 3em;
            margin-bottom: 30px;
            text-shadow: 0 0 20px #00ff88;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        @keyframes titleGlow {
            from { text-shadow: 0 0 20px #00ff88; }
            to { text-shadow: 0 0 30px #00ff88, 0 0 40px #00ff88; }
        }
        
        .level-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .level-card {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border: 3px solid #3498db;
            border-radius: 20px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .level-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: #00ff88;
            box-shadow: 0 15px 30px rgba(0,255,136,0.3);
        }
        
        .level-card.selected {
            border-color: #f39c12;
            background: linear-gradient(145deg, #e67e22, #f39c12);
            transform: scale(1.05);
        }
        
        .level-preview {
            width: 100%;
            height: 120px;
            background: #1a1a1a;
            border-radius: 10px;
            margin-bottom: 15px;
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            grid-template-rows: repeat(6, 1fr);
            gap: 1px;
            padding: 5px;
        }
        
        .preview-cell {
            border-radius: 2px;
        }
        
        .preview-empty { background: #34495e; }
        .preview-wall { background: #7f8c8d; }
        .preview-block { background: #8b4513; }
        .preview-spawn { background: #3498db; }
        
        .level-info h3 {
            font-size: 1.4em;
            margin-bottom: 10px;
            color: #00ff88;
        }
        
        .difficulty {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .difficulty.easy { background: #27ae60; }
        .difficulty.medium { background: #f39c12; }
        .difficulty.hard { background: #e74c3c; }
        .difficulty.expert { background: #9b59b6; }
        
        .level-stats {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 0.9em;
        }
        
        .level-description {
            font-size: 0.9em;
            color: #bdc3c7;
            line-height: 1.4;
        }
        
        /* Countdown Screen */
        .countdown-screen {
            text-align: center;
            background: radial-gradient(circle, #2c3e50, #1a1a1a);
        }
        
        .countdown-number {
            font-size: 15em;
            font-weight: bold;
            text-shadow: 0 0 50px currentColor;
            animation: countdownPulse 1s ease-in-out;
            margin: 20px 0;
        }
        
        .countdown-number.three { color: #e74c3c; }
        .countdown-number.two { color: #f39c12; }
        .countdown-number.one { color: #f1c40f; }
        .countdown-number.go { color: #27ae60; font-size: 8em; }
        
        @keyframes countdownPulse {
            0% { transform: scale(0.5); opacity: 0; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .countdown-text {
            font-size: 2em;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        /* Game Screen */
        .game-container {
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 20px;
            border: 3px solid #00ff88;
            box-shadow: 0 0 30px rgba(0,255,136,0.3);
            max-width: 1000px;
            width: 100%;
        }
        
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .level-info-game {
            text-align: left;
        }
        
        .level-name {
            font-size: 1.5em;
            color: #00ff88;
            margin-bottom: 5px;
        }
        
        .stats {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .stat {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            padding: 8px 12px;
            border-radius: 8px;
            border: 2px solid #3498db;
            font-weight: bold;
            min-width: 100px;
            font-size: 0.9em;
        }
        
        .game-board {
            display: inline-grid;
            gap: 1px;
            background: #2c3e50;
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #e74c3c;
            box-shadow: 0 0 20px rgba(231,76,60,0.5);
        }
        
        .cell {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border-radius: 3px;
            position: relative;
            transition: all 0.2s ease;
        }
        
        .empty { background: #34495e; }
        .wall { background: #7f8c8d; box-shadow: inset 0 0 5px rgba(0,0,0,0.5); }
        .block { background: #8b4513; box-shadow: 0 2px 4px rgba(0,0,0,0.3); }
        .player { 
            background: radial-gradient(circle, #3498db, #2980b9); 
            border: 2px solid white;
            box-shadow: 0 0 15px rgba(52,152,219,0.8);
            animation: playerPulse 1.5s ease-in-out infinite alternate;
        }
        .bomb { 
            background: radial-gradient(circle, #e74c3c, #c0392b); 
            border: 2px solid #f39c12;
            animation: bombPulse 0.5s ease-in-out infinite alternate;
            box-shadow: 0 0 15px rgba(231,76,60,0.8);
        }
        .powerup { 
            background: radial-gradient(circle, #f39c12, #e67e22); 
            border: 2px solid white;
            animation: powerupGlow 1s ease-in-out infinite alternate;
            box-shadow: 0 0 15px rgba(243,156,18,0.8);
        }
        .explosion { 
            background: radial-gradient(circle, #ff6b35, #ff4757); 
            animation: explosionFlash 0.3s ease-out;
            box-shadow: 0 0 20px rgba(255,107,53,1);
        }
        
        @keyframes playerPulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.05); }
        }
        
        @keyframes bombPulse {
            0% { transform: scale(1); box-shadow: 0 0 15px rgba(231,76,60,0.8); }
            100% { transform: scale(1.1); box-shadow: 0 0 25px rgba(231,76,60,1); }
        }
        
        @keyframes powerupGlow {
            0% { transform: scale(1); box-shadow: 0 0 15px rgba(243,156,18,0.8); }
            100% { transform: scale(1.05); box-shadow: 0 0 25px rgba(243,156,18,1); }
        }
        
        @keyframes explosionFlash {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(1.2); opacity: 0.7; }
        }
        
        .controls {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #95a5a6;
        }
        
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        
        .btn-primary { background: linear-gradient(45deg, #27ae60, #2ecc71); }
        .btn-primary:hover { background: linear-gradient(45deg, #2ecc71, #27ae60); }
        
        .btn-secondary { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }
        .btn-secondary:hover { background: linear-gradient(45deg, #7f8c8d, #95a5a6); }
        
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .btn-danger:hover { background: linear-gradient(45deg, #c0392b, #e74c3c); }
        
        #message {
            margin-top: 20px;
            font-size: 16px;
            font-weight: bold;
            min-height: 25px;
            padding: 10px;
            background: rgba(52,73,94,0.8);
            border-radius: 10px;
            border: 2px solid #f39c12;
        }
        
        .instructions {
            background: rgba(44,62,80,0.9);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 2px solid #9b59b6;
        }
        
        .key {
            background: #34495e;
            padding: 3px 8px;
            border-radius: 5px;
            border: 1px solid #7f8c8d;
            font-family: monospace;
            font-weight: bold;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .title { font-size: 2em; }
            .level-grid { grid-template-columns: 1fr; }
            .countdown-number { font-size: 8em; }
            .game-header { flex-direction: column; gap: 15px; }
            .stats { justify-content: center; }
        }
    </style>
</head>
<body>
    <!-- Level Selection Screen -->
    <div class="screen active" id="level-selection-screen">
        <div class="level-selection">
            <h1 class="title">🎮 BOMBERMAN</h1>
            <p style="font-size: 1.2em; margin-bottom: 30px; color: #bdc3c7;">Choose your battlefield!</p>
            
            <div class="level-grid" id="level-grid">
                <!-- Levels will be populated by JavaScript -->
            </div>
            
            <div style="margin-top: 30px;">
                <button onclick="startSelectedLevel()" class="btn-primary" style="font-size: 1.2em; padding: 15px 30px;">
                    🚀 Start Game
                </button>
            </div>
        </div>
    </div>
    
    <!-- Countdown Screen -->
    <div class="screen" id="countdown-screen">
        <div class="countdown-screen">
            <div class="countdown-number" id="countdown-number">3</div>
            <div class="countdown-text" id="countdown-text">Get Ready!</div>
        </div>
    </div>
    
    <!-- Game Screen -->
    <div class="screen" id="game-screen">
        <div class="game-container">
            <div class="game-header">
                <div class="level-info-game">
                    <div class="level-name" id="current-level-name">Classic Arena</div>
                    <div style="font-size: 0.9em; color: #bdc3c7;" id="current-level-desc">Traditional Bomberman battlefield</div>
                </div>
                
                <div class="stats">
                    <div class="stat">💣 Bombs: <span id="bomb-count">1</span></div>
                    <div class="stat">🔥 Range: <span id="bomb-range">1</span></div>
                    <div class="stat">⚡ Speed: <span id="speed">1.0</span>x</div>
                    <div class="stat">❤️ Lives: <span id="lives">3</span></div>
                </div>
            </div>
            
            <div class="instructions">
                <strong>🎯 Controls:</strong>
                <span class="key">↑↓←→</span> Move | <span class="key">Space</span> Bomb | 
                💣 = +1 Bomb | 🔥 = +1 Range | ⚡ = +Speed (30% spawn chance)
            </div>
            
            <div class="game-board" id="game-board"></div>
            
            <div class="controls">
                <button onclick="pauseGame()" class="btn-secondary">⏸️ Pause</button>
                <button onclick="resetGame()" class="btn-secondary">🔄 Reset</button>
                <button onclick="backToLevelSelect()" class="btn-secondary">🏠 Level Select</button>
                <button onclick="testPowerUps()" class="btn-danger">💫 Test Power-ups</button>
                <button onclick="testBombs()" class="btn-danger">💥 Test Bomb</button>
            </div>
            
            <div id="message">🎮 Use arrow keys to move, spacebar to place bombs!</div>
        </div>
    </div>

    <script>
        // Game state
        let currentScreen = 'level-selection';
        let selectedLevel = null;
        let gameBoard = [];
        let player = { x: 1, y: 1 };
        let bombs = [];
        let powerUps = [];
        let explosions = [];
        let gameRunning = false;
        let gamePaused = false;
        
        // Player stats
        let playerStats = {
            maxBombs: 1,
            bombRange: 1,
            speed: 1.0,
            lives: 3,
            activeBombs: 0
        };
        
        // Level definitions
        const levels = [
            {
                id: 'classic',
                name: 'Classic Arena',
                difficulty: 'easy',
                description: 'Traditional Bomberman battlefield with balanced block placement',
                width: 15,
                height: 13,
                blockDensity: 0.3,
                powerUpChance: 0.3,
                specialFeatures: 'Balanced gameplay'
            },
            {
                id: 'maze',
                name: 'Labyrinth',
                difficulty: 'medium',
                description: 'Complex maze with narrow passages and strategic chokepoints',
                width: 17,
                height: 13,
                blockDensity: 0.4,
                powerUpChance: 0.25,
                specialFeatures: 'Narrow passages'
            },
            {
                id: 'open',
                name: 'Open Field',
                difficulty: 'easy',
                description: 'Wide open spaces with fewer obstacles for fast-paced action',
                width: 19,
                height: 15,
                blockDensity: 0.2,
                powerUpChance: 0.35,
                specialFeatures: 'Fast-paced action'
            },
            {
                id: 'fortress',
                name: 'Fortress Siege',
                difficulty: 'hard',
                description: 'Dense block formations creating fortress-like structures',
                width: 15,
                height: 13,
                blockDensity: 0.5,
                powerUpChance: 0.2,
                specialFeatures: 'Dense obstacles'
            },
            {
                id: 'chaos',
                name: 'Chaos Chamber',
                difficulty: 'expert',
                description: 'Unpredictable layout with maximum challenge and surprises',
                width: 21,
                height: 15,
                blockDensity: 0.45,
                powerUpChance: 0.4,
                specialFeatures: 'Maximum chaos'
            }
        ];
        
        // Screen management
        function showScreen(screenId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            document.getElementById(screenId).classList.add('active');
            currentScreen = screenId;
        }

        // Level selection functions
        function populateLevelSelection() {
            const levelGrid = document.getElementById('level-grid');
            levelGrid.innerHTML = '';

            levels.forEach((level, index) => {
                const levelCard = document.createElement('div');
                levelCard.className = 'level-card';
                levelCard.onclick = () => selectLevel(index);

                levelCard.innerHTML = `
                    <div class="level-preview" id="preview-${level.id}"></div>
                    <div class="level-info">
                        <h3>${level.name}</h3>
                        <div class="difficulty ${level.difficulty}">${level.difficulty.toUpperCase()}</div>
                        <div class="level-stats">
                            <span>📏 ${level.width}×${level.height}</span>
                            <span>🧱 ${Math.round(level.blockDensity * 100)}% blocks</span>
                            <span>💎 ${Math.round(level.powerUpChance * 100)}% power-ups</span>
                        </div>
                        <div class="level-description">${level.description}</div>
                        <div style="margin-top: 10px; font-size: 0.8em; color: #3498db;">
                            ✨ ${level.specialFeatures}
                        </div>
                    </div>
                `;

                levelGrid.appendChild(levelCard);
                generateLevelPreview(level);
            });
        }

        function generateLevelPreview(level) {
            const preview = document.getElementById(`preview-${level.id}`);
            const previewWidth = 10;
            const previewHeight = 6;

            for (let y = 0; y < previewHeight; y++) {
                for (let x = 0; x < previewWidth; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'preview-cell';

                    if (x === 0 || x === previewWidth - 1 || y === 0 || y === previewHeight - 1) {
                        cell.classList.add('preview-wall');
                    } else if (x % 2 === 0 && y % 2 === 0) {
                        cell.classList.add('preview-wall');
                    } else if (x === 1 && y === 1) {
                        cell.classList.add('preview-spawn');
                    } else if (Math.random() < level.blockDensity) {
                        cell.classList.add('preview-block');
                    } else {
                        cell.classList.add('preview-empty');
                    }

                    preview.appendChild(cell);
                }
            }
        }

        function selectLevel(index) {
            selectedLevel = levels[index];
            updateLevelSelection();
        }

        function updateLevelSelection() {
            document.querySelectorAll('.level-card').forEach((card, index) => {
                card.classList.toggle('selected', index === levels.indexOf(selectedLevel));
            });
        }

        function startSelectedLevel() {
            if (!selectedLevel) {
                alert('Please select a level first!');
                return;
            }

            showCountdown();
        }

        // Countdown functions
        function showCountdown() {
            showScreen('countdown-screen');

            const countdownNumber = document.getElementById('countdown-number');
            const countdownText = document.getElementById('countdown-text');

            let count = 3;
            const countdownWords = ['three', 'two', 'one'];

            function updateCountdown() {
                if (count > 0) {
                    countdownNumber.textContent = count;
                    countdownNumber.className = `countdown-number ${countdownWords[count - 1]}`;
                    countdownText.textContent = count === 3 ? 'Get Ready!' : count === 2 ? 'Set...' : 'Go!';

                    // Trigger animation
                    countdownNumber.style.animation = 'none';
                    setTimeout(() => {
                        countdownNumber.style.animation = 'countdownPulse 1s ease-in-out';
                    }, 10);

                    count--;
                    setTimeout(updateCountdown, 1000);
                } else {
                    // Show GO!
                    countdownNumber.textContent = 'GO!';
                    countdownNumber.className = 'countdown-number go';
                    countdownText.textContent = 'Fight!';

                    countdownNumber.style.animation = 'none';
                    setTimeout(() => {
                        countdownNumber.style.animation = 'countdownPulse 1s ease-in-out';
                    }, 10);

                    setTimeout(() => {
                        startGame();
                    }, 1000);
                }
            }

            updateCountdown();
        }

        // Game functions
        function startGame() {
            showScreen('game-screen');

            // Update level info in game screen
            document.getElementById('current-level-name').textContent = selectedLevel.name;
            document.getElementById('current-level-desc').textContent = selectedLevel.description;

            // Initialize game with selected level
            createBoard();
            renderBoard();
            updateStats();

            gameRunning = true;
            gamePaused = false;

            showMessage(`🎮 ${selectedLevel.name} started! Destroy blocks to find power-ups!`);
        }

        function backToLevelSelect() {
            gameRunning = false;
            gamePaused = false;
            resetGameState();
            showScreen('level-selection-screen');
        }

        function pauseGame() {
            if (!gameRunning) return;

            gamePaused = !gamePaused;
            const pauseBtn = event.target;

            if (gamePaused) {
                pauseBtn.textContent = '▶️ Resume';
                showMessage('⏸️ Game paused. Click Resume to continue.');
            } else {
                pauseBtn.textContent = '⏸️ Pause';
                showMessage('🎮 Game resumed! Keep playing!');
            }
        }

        function resetGame() {
            resetGameState();
            startGame();
        }

        function resetGameState() {
            player = { x: 1, y: 1 };
            bombs = [];
            powerUps = [];
            explosions = [];
            playerStats = { maxBombs: 1, bombRange: 1, speed: 1.0, lives: 3, activeBombs: 0 };
        }

        // Initialize the game
        function initGame() {
            populateLevelSelection();
            selectedLevel = levels[0]; // Default to first level
            updateLevelSelection();
        }

        // Board creation and rendering
        function createBoard() {
            const level = selectedLevel;
            gameBoard = [];

            for (let y = 0; y < level.height; y++) {
                gameBoard[y] = [];
                for (let x = 0; x < level.width; x++) {
                    if (x === 0 || x === level.width - 1 || y === 0 || y === level.height - 1) {
                        gameBoard[y][x] = 'wall';
                    } else if (x % 2 === 0 && y % 2 === 0) {
                        gameBoard[y][x] = 'wall';
                    } else if ((x <= 2 && y <= 2)) {
                        gameBoard[y][x] = 'empty'; // Player spawn area
                    } else if (Math.random() < level.blockDensity) {
                        gameBoard[y][x] = 'block';
                    } else {
                        gameBoard[y][x] = 'empty';
                    }
                }
            }

            // Set up game board grid
            const boardElement = document.getElementById('game-board');
            boardElement.style.gridTemplateColumns = `repeat(${level.width}, 30px)`;
            boardElement.style.gridTemplateRows = `repeat(${level.height}, 30px)`;
        }

        function renderBoard() {
            const boardElement = document.getElementById('game-board');
            boardElement.innerHTML = '';

            const level = selectedLevel;

            for (let y = 0; y < level.height; y++) {
                for (let x = 0; x < level.width; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;

                    // Set base cell type
                    if (gameBoard[y][x] === 'wall') {
                        cell.classList.add('wall');
                        cell.textContent = '🧱';
                    } else if (gameBoard[y][x] === 'block') {
                        cell.classList.add('block');
                        cell.textContent = '📦';
                    } else {
                        cell.classList.add('empty');
                    }

                    // Add player
                    if (x === player.x && y === player.y) {
                        cell.classList.add('player');
                        cell.textContent = '😊';
                    }

                    // Add bombs
                    bombs.forEach(bomb => {
                        if (bomb.x === x && bomb.y === y) {
                            cell.classList.add('bomb');
                            cell.textContent = '💣';
                        }
                    });

                    // Add power-ups
                    powerUps.forEach(powerUp => {
                        if (powerUp.x === x && powerUp.y === y) {
                            cell.classList.add('powerup');
                            cell.textContent = powerUp.symbol;
                        }
                    });

                    // Add explosions
                    explosions.forEach(explosion => {
                        if (explosion.x === x && explosion.y === y) {
                            cell.classList.add('explosion');
                            cell.textContent = '💥';
                        }
                    });

                    boardElement.appendChild(cell);
                }
            }
        }

        function updateStats() {
            document.getElementById('bomb-count').textContent = playerStats.maxBombs;
            document.getElementById('bomb-range').textContent = playerStats.bombRange;
            document.getElementById('speed').textContent = playerStats.speed.toFixed(1);
            document.getElementById('lives').textContent = playerStats.lives;
        }

        function showMessage(text) {
            document.getElementById('message').textContent = text;
        }

        // Movement and controls
        function movePlayer(dx, dy) {
            if (!gameRunning || gamePaused) return;

            const newX = player.x + dx;
            const newY = player.y + dy;

            if (canMoveTo(newX, newY)) {
                player.x = newX;
                player.y = newY;

                // Check for power-up collection
                checkPowerUpCollection();
                renderBoard();
            }
        }

        function canMoveTo(x, y) {
            const level = selectedLevel;
            if (x < 0 || x >= level.width || y < 0 || y >= level.height) return false;
            if (gameBoard[y][x] === 'wall' || gameBoard[y][x] === 'block') return false;

            // Check for bombs (can't walk through them)
            return !bombs.some(bomb => bomb.x === x && bomb.y === y);
        }

        function checkPowerUpCollection() {
            powerUps = powerUps.filter(powerUp => {
                if (powerUp.x === player.x && powerUp.y === player.y) {
                    collectPowerUp(powerUp);
                    return false; // Remove collected power-up
                }
                return true;
            });
        }

        function collectPowerUp(powerUp) {
            switch (powerUp.type) {
                case 'bombs':
                    playerStats.maxBombs++;
                    showMessage(`💣 Collected bomb power-up! Max bombs: ${playerStats.maxBombs}`);
                    break;
                case 'flames':
                    playerStats.bombRange++;
                    showMessage(`🔥 Collected flame power-up! Bomb range: ${playerStats.bombRange}`);
                    break;
                case 'speed':
                    playerStats.speed += 0.2;
                    showMessage(`⚡ Collected speed power-up! Speed: ${playerStats.speed.toFixed(1)}x`);
                    break;
            }
            updateStats();
        }

        // Bomb mechanics
        function placeBomb(x, y) {
            if (!gameRunning || gamePaused) return;

            if (playerStats.activeBombs >= playerStats.maxBombs) {
                showMessage("💣 Cannot place bomb - maximum active bombs reached!");
                return;
            }
            if (bombs.some(bomb => bomb.x === x && bomb.y === y)) {
                showMessage("💣 Cannot place bomb - position occupied!");
                return;
            }

            const bomb = { x, y, timer: 3 };
            bombs.push(bomb);
            playerStats.activeBombs++;

            showMessage(`💣 Bomb placed! Exploding in 3 seconds... (${playerStats.activeBombs}/${playerStats.maxBombs})`);

            // Explode after 3 seconds
            setTimeout(() => explodeBomb(bomb), 3000);
            renderBoard();
        }

        function explodeBomb(bomb) {
            // Remove bomb
            bombs = bombs.filter(b => b !== bomb);
            playerStats.activeBombs--;

            // Create explosion
            const explosionCells = [{ x: bomb.x, y: bomb.y }];
            let blocksDestroyed = 0;
            let powerUpsSpawned = 0;

            // Explosion in 4 directions
            for (let dir = 0; dir < 4; dir++) {
                const dx = [0, 1, 0, -1][dir];
                const dy = [-1, 0, 1, 0][dir];

                for (let i = 1; i <= playerStats.bombRange; i++) {
                    const x = bomb.x + dx * i;
                    const y = bomb.y + dy * i;

                    const level = selectedLevel;
                    if (x < 0 || x >= level.width || y < 0 || y >= level.height) break;
                    if (gameBoard[y][x] === 'wall') break;

                    explosionCells.push({ x, y });

                    // Destroy blocks and spawn power-ups
                    if (gameBoard[y][x] === 'block') {
                        gameBoard[y][x] = 'empty';
                        blocksDestroyed++;

                        // Power-up spawn chance based on level
                        if (Math.random() < selectedLevel.powerUpChance) {
                            const types = ['bombs', 'flames', 'speed'];
                            const symbols = ['💣', '🔥', '⚡'];
                            const type = types[Math.floor(Math.random() * types.length)];
                            const symbol = symbols[types.indexOf(type)];

                            powerUps.push({ x, y, type, symbol });
                            powerUpsSpawned++;
                        }
                        break;
                    }
                }
            }

            // Show explosion
            explosions = explosionCells;
            renderBoard();

            // Clear explosion after 500ms
            setTimeout(() => {
                explosions = [];
                renderBoard();
            }, 500);

            showMessage(`💥 Explosion! Destroyed ${blocksDestroyed} blocks, spawned ${powerUpsSpawned} power-ups!`);
        }

        // Test functions
        function testPowerUps() {
            powerUps = [
                { x: 3, y: 3, type: 'bombs', symbol: '💣' },
                { x: 5, y: 5, type: 'flames', symbol: '🔥' },
                { x: 7, y: 7, type: 'speed', symbol: '⚡' }
            ];
            renderBoard();
            showMessage("💫 Test power-ups spawned! Walk over them to collect and see stats increase!");
        }

        function testBombs() {
            if (playerStats.activeBombs < playerStats.maxBombs) {
                placeBomb(player.x, player.y);
                showMessage("💥 Test bomb placed! It will explode in 3 seconds and may spawn power-ups!");
            } else {
                showMessage("💣 Cannot place bomb - maximum active bombs reached!");
            }
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || gamePaused) return;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    movePlayer(0, -1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    movePlayer(0, 1);
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    movePlayer(-1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    movePlayer(1, 0);
                    break;
                case ' ':
                    e.preventDefault();
                    placeBomb(player.x, player.y);
                    break;
                case 'Escape':
                    e.preventDefault();
                    pauseGame();
                    break;
            }
        });

        // Initialize on load
        window.onload = initGame;
    </script>
</body>
</html>
