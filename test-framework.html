<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Framework Test</title>
</head>
<body>
    <h1>Framework Loading Test</h1>
    <div id="status">Loading...</div>
    <div id="details"></div>

    <!-- Load framework scripts -->
    <script src="./static/js/framework/utils.js"></script>
    <script src="./static/js/framework/components.js"></script>
    <script src="./static/js/framework/systems.js"></script>
    <script src="./static/js/framework/core.js"></script>
    <script src="./static/js/game/multiplayer.js"></script>
    <script src="./static/js/game/map.js"></script>
    <script src="./static/js/game/player.js"></script>
    <script src="./static/js/game/bomb.js"></script>
    <script src="./static/js/game/scenes.js"></script>
    <script src="./static/js/game/gameScene.js"></script>
    <script src="./static/js/game/game.js"></script>

    <script>
        function checkFramework() {
            const status = document.getElementById('status');
            const details = document.getElementById('details');
            
            const checks = {
                'Utils': typeof window.Utils,
                'Systems': typeof window.Systems,
                'GameFramework': typeof window.GameFramework,
                'BombermanGame': typeof window.BombermanGame,
                'GameMap': typeof window.GameMap,
                'Player': typeof window.Player,
                'Bomb': typeof window.Bomb,
                'MultiplayerManager': typeof window.MultiplayerManager
            };
            
            let allLoaded = true;
            let detailsHtml = '<h3>Framework Components:</h3><ul>';
            
            for (const [name, type] of Object.entries(checks)) {
                const loaded = type !== 'undefined';
                if (!loaded) allLoaded = false;
                
                detailsHtml += `<li style="color: ${loaded ? 'green' : 'red'}">
                    ${name}: ${type} ${loaded ? '✓' : '✗'}
                </li>`;
            }
            
            detailsHtml += '</ul>';
            
            if (allLoaded) {
                status.innerHTML = '<span style="color: green;">✓ All framework components loaded successfully!</span>';
                detailsHtml += '<button onclick="testGame()">Test Game Initialization</button>';
            } else {
                status.innerHTML = '<span style="color: red;">✗ Some framework components failed to load</span>';
            }
            
            details.innerHTML = detailsHtml;
        }
        
        function testGame() {
            try {
                console.log('Testing game initialization...');
                const game = BombermanGame.start();
                document.getElementById('details').innerHTML += '<p style="color: green;">✓ Game initialized successfully!</p>';
            } catch (error) {
                console.error('Game initialization failed:', error);
                document.getElementById('details').innerHTML += `<p style="color: red;">✗ Game initialization failed: ${error.message}</p>`;
            }
        }
        
        // Check framework after a delay
        setTimeout(checkFramework, 1000);
    </script>
</body>
</html>
