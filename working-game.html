<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman - Multiplayer Game</title>
    <link rel="stylesheet" href="./static/style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #07131F;
            font-family: 'EngraversGothic', sans-serif;
        }
        
        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }
        
        .scene {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .scene.active {
            display: flex;
        }
        
        .loading-screen {
            background: #07131F;
            color: white;
            z-index: 10000;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #34495e;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .nickname-scene {
            background: #07131F;
        }
        
        .nickname-form {
            background: #2c3e50;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .nickname-form h1 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 5px;
        }
        
        .nickname-input {
            padding: 15px 20px;
            font-size: 1.2rem;
            border: 2px solid #34495e;
            border-radius: 5px;
            background: #34495e;
            color: white;
            text-align: center;
            min-width: 300px;
            margin-bottom: 20px;
        }
        
        .nickname-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }
        
        .game-button {
            padding: 15px 30px;
            font-size: 1.1rem;
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            margin: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'EngraversGothic', sans-serif;
            transition: all 0.3s ease;
        }
        
        .game-button.primary { background: #3498db; }
        .game-button.secondary { background: #e74c3c; }
        .game-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .lobby-scene {
            background: #07131F;
        }
        
        .lobby-container {
            background: #2c3e50;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            min-width: 600px;
            max-width: 800px;
        }
        
        .lobby-title {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #3498db;
        }
        
        .player-info {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: white;
        }
        
        .status-message {
            font-size: 1rem;
            color: #f39c12;
            margin-bottom: 20px;
        }
        
        .chat-container {
            background: #34495e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .chat-messages {
            height: 150px;
            overflow-y: auto;
            background: #2c3e50;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            color: white;
            font-size: 14px;
        }
        
        .chat-input-container {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: #2c3e50;
            color: white;
        }
        
        .error-message {
            background: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Loading Scene -->
        <div id="loading-scene" class="scene loading-screen active">
            <div class="loading-spinner"></div>
            <h2 id="loading-text">Loading Bomberman...</h2>
            <p id="loading-status">Initializing game framework...</p>
        </div>
        
        <!-- Error Scene -->
        <div id="error-scene" class="scene" style="background: #07131F;">
            <div class="error-message">
                <h3>Error Loading Game</h3>
                <p id="error-text">An error occurred while loading the game.</p>
                <button class="game-button secondary" onclick="location.reload()">Reload Game</button>
                <button class="game-button primary" onclick="showSimpleVersion()">Try Simple Version</button>
            </div>
        </div>
        
        <!-- Nickname Scene -->
        <div id="nickname-scene" class="scene nickname-scene">
            <div class="nickname-form">
                <h1>Enter Your Nickname</h1>
                <input type="text" id="nickname-input" class="nickname-input" placeholder="Enter nickname..." maxlength="20">
                <br>
                <button id="join-button" class="game-button primary">Join Game</button>
                <button id="back-button" class="game-button secondary">Back to Main Menu</button>
            </div>
        </div>
        
        <!-- Lobby Scene -->
        <div id="lobby-scene" class="scene lobby-scene">
            <div class="lobby-container">
                <h1 class="lobby-title">Waiting for Players</h1>
                <div id="player-info" class="player-info">
                    Player: <span id="player-name"></span><br>
                    <span id="player-count">Players: 1/4</span>
                </div>
                <div id="status-message" class="status-message">Connecting to multiplayer server...</div>
                
                <div class="chat-container">
                    <h3 style="color: white; margin-top: 0;">Chat</h3>
                    <div id="chat-messages" class="chat-messages">
                        <div style="color: #3498db;">Welcome to Bomberman! Chat with other players while waiting...</div>
                    </div>
                    <div class="chat-input-container">
                        <input type="text" id="chat-input" class="chat-input" placeholder="Type a message..." maxlength="200">
                        <button id="send-chat" class="game-button primary" style="margin: 0;">Send</button>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button id="start-game-button" class="game-button primary" style="display: none;">Start Game</button>
                    <button id="leave-lobby-button" class="game-button secondary">Leave Lobby</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load framework scripts first -->
    <script src="./static/js/framework/utils.js"></script>
    <script src="./static/js/framework/components.js"></script>
    <script src="./static/js/framework/systems.js"></script>
    <script src="./static/js/framework/core.js"></script>
    <script src="./static/js/game/multiplayer.js"></script>
    <script src="./static/js/game/map.js"></script>
    <script src="./static/js/game/player.js"></script>
    <script src="./static/js/game/bomb.js"></script>
    <script src="./static/js/game/scenes.js"></script>
    <script src="./static/js/game/gameScene.js"></script>
    <script src="./static/js/game/game.js"></script>

    <script>
        // Game state
        let currentScene = 'loading';
        let playerNickname = '';
        let gameMode = 'auto'; // 'auto' tries full game, 'simple' uses simple version
        let chatMessages = [];
        let gameInstance = null;

        // Scene management
        function showScene(sceneName) {
            document.querySelectorAll('.scene').forEach(scene => {
                scene.classList.remove('active');
            });

            const targetScene = document.getElementById(sceneName + '-scene');
            if (targetScene) {
                targetScene.classList.add('active');
                currentScene = sceneName;
                console.log('Switched to scene:', sceneName);
            }
        }

        function showError(message) {
            document.getElementById('error-text').textContent = message;
            showScene('error');
        }

        function showSimpleVersion() {
            gameMode = 'simple';
            showScene('nickname');
        }

        function updateLoadingText(text) {
            const loadingStatus = document.getElementById('loading-status');
            if (loadingStatus) {
                loadingStatus.textContent = text;
            }
        }
        
        // Chat system
        function addChatMessage(nickname, message, isSystem = false) {
            const chatContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '5px';
            
            if (isSystem) {
                messageDiv.style.color = '#3498db';
                messageDiv.textContent = message;
            } else {
                messageDiv.innerHTML = `<strong style="color: #f39c12;">${nickname}:</strong> ${message}`;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function sendChatMessage() {
            const chatInput = document.getElementById('chat-input');
            const message = chatInput.value.trim();
            
            if (message) {
                addChatMessage(playerNickname, message);
                chatInput.value = '';
                
                // Simulate other players (for demo)
                if (gameMode === 'simple') {
                    setTimeout(() => {
                        const responses = [
                            "Ready to play!",
                            "Let's do this!",
                            "Good luck everyone!",
                            "Can't wait to start!"
                        ];
                        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                        addChatMessage('Player' + Math.floor(Math.random() * 3 + 2), randomResponse);
                    }, 1000 + Math.random() * 2000);
                }
            }
        }
        
        // Game initialization
        function initializeGame() {
            console.log('Starting game initialization...');
            updateLoadingText('Checking framework dependencies...');

            // Check framework availability with retries
            let retryCount = 0;
            const maxRetries = 10;

            function checkFramework() {
                const hasFramework = typeof window.GameFramework !== 'undefined' &&
                                   typeof window.BombermanGame !== 'undefined' &&
                                   typeof window.Utils !== 'undefined' &&
                                   typeof window.Systems !== 'undefined';

                console.log('Framework check (attempt ' + (retryCount + 1) + '):', {
                    GameFramework: typeof window.GameFramework,
                    BombermanGame: typeof window.BombermanGame,
                    Utils: typeof window.Utils,
                    Systems: typeof window.Systems,
                    hasFramework: hasFramework
                });

                if (hasFramework && gameMode === 'auto') {
                    updateLoadingText('Initializing full multiplayer game...');
                    console.log('All framework components loaded, starting full game...');

                    try {
                        // Clear the existing game container content
                        const gameContainer = document.querySelector('.game-container');
                        gameContainer.innerHTML = '';

                        // Initialize the full game
                        gameInstance = BombermanGame.start();
                        console.log('Full game initialized successfully');
                        return; // Full game takes over from here
                    } catch (error) {
                        console.error('Full game initialization failed:', error);
                        console.error('Error details:', error.stack);

                        // Restore the original HTML content
                        location.reload(); // Simple way to restore original state
                    }
                } else if (retryCount < maxRetries) {
                    retryCount++;
                    updateLoadingText(`Loading framework components... (${retryCount}/${maxRetries})`);
                    setTimeout(checkFramework, 500);
                } else {
                    console.log('Framework not available after retries, using simple mode');
                    initializeSimpleGame();
                }
            }

            // Start checking after a brief delay
            setTimeout(checkFramework, 100);
        }
        
        function initializeSimpleGame() {
            console.log('Initializing simple game mode...');
            updateLoadingText('Loading simple game mode...');

            setTimeout(() => {
                try {
                    showScene('nickname');
                    setupSimpleGameHandlers();
                    console.log('Simple game mode initialized successfully');
                } catch (error) {
                    console.error('Simple game initialization failed:', error);
                    showError('Failed to initialize game. Please refresh the page.');
                }
            }, 500);
        }
        
        function setupSimpleGameHandlers() {
            // Get elements
            const nicknameInput = document.getElementById('nickname-input');
            const joinButton = document.getElementById('join-button');
            const backButton = document.getElementById('back-button');
            const playerNameSpan = document.getElementById('player-name');
            const statusMessage = document.getElementById('status-message');
            const startGameButton = document.getElementById('start-game-button');
            const leaveLobbyButton = document.getElementById('leave-lobby-button');
            const chatInput = document.getElementById('chat-input');
            const sendChatButton = document.getElementById('send-chat');
            
            // Focus nickname input
            nicknameInput.focus();
            
            // Nickname form handlers
            joinButton.addEventListener('click', function() {
                const nickname = nicknameInput.value.trim();
                if (nickname.length >= 2) {
                    playerNickname = nickname;
                    playerNameSpan.textContent = nickname;
                    showScene('lobby');
                    
                    // Simulate lobby progression
                    addChatMessage('System', `${nickname} joined the game!`, true);
                    
                    setTimeout(() => {
                        statusMessage.textContent = 'Waiting for more players... (2-4 players needed)';
                        document.getElementById('player-count').textContent = 'Players: 2/4';
                        addChatMessage('System', 'Player2 joined the game!', true);
                    }, 1000);
                    
                    setTimeout(() => {
                        statusMessage.textContent = 'Ready to start! Click "Start Game" to begin.';
                        startGameButton.style.display = 'inline-block';
                        document.getElementById('player-count').textContent = 'Players: 3/4';
                        addChatMessage('System', 'Player3 joined the game!', true);
                    }, 3000);
                } else {
                    alert('Nickname must be at least 2 characters long');
                }
            });
            
            backButton.addEventListener('click', function() {
                window.location.href = '/';
            });
            
            // Lobby handlers
            startGameButton.addEventListener('click', function() {
                statusMessage.textContent = 'Starting game...';
                addChatMessage('System', 'Game starting in 3... 2... 1...', true);
                
                setTimeout(() => {
                    alert('🎮 Game Started!\n\nThis is the demo version.\nIn the full game, you would now see:\n• Game map with walls and blocks\n• Your player character\n• Bomb placement controls\n• Real-time multiplayer action\n\nTo enable full multiplayer, ensure the WebSocket server is running.');
                }, 2000);
            });
            
            leaveLobbyButton.addEventListener('click', function() {
                showScene('nickname');
                nicknameInput.value = '';
                nicknameInput.focus();
                chatMessages = [];
                document.getElementById('chat-messages').innerHTML = '<div style="color: #3498db;">Welcome to Bomberman! Chat with other players while waiting...</div>';
            });
            
            // Chat handlers
            sendChatButton.addEventListener('click', sendChatMessage);
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });
            
            // Enter key support for nickname
            nicknameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    joinButton.click();
                }
            });
            
            console.log('Simple game handlers initialized');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting initialization...');
            initializeGame();

            // Fallback: if still on loading screen after 10 seconds, force simple mode
            setTimeout(() => {
                if (currentScene === 'loading') {
                    console.warn('Loading timeout - forcing simple mode');
                    gameMode = 'simple';
                    initializeSimpleGame();
                }
            }, 10000);
        });
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', function(e) {
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(e.code)) {
                e.preventDefault();
            }
        });
        
        console.log('Working game script loaded');
    </script>
</body>
</html>
