<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Bomberman Game Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #2c3e50;
            color: white;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #34495e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .warning { background: #f39c12; }
        .info { background: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #7f8c8d; cursor: not-allowed; }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            background: #2c3e50;
        }
        .feature-list li.pass { background: #27ae60; }
        .feature-list li.fail { background: #e74c3c; }
        .feature-list li.pending { background: #f39c12; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 Complete Bomberman Game Test Suite</h1>
        
        <div class="test-section">
            <h2>Test Overview</h2>
            <p>This comprehensive test validates all game features and requirements:</p>
            <div class="test-grid">
                <div>
                    <h3>Core Features</h3>
                    <ul class="feature-list" id="core-features">
                        <li id="test-level-selection">Level Selection System</li>
                        <li id="test-multiplayer">Multiplayer Support (2-4 players)</li>
                        <li id="test-player-movement">Player Movement</li>
                        <li id="test-bomb-mechanics">Bomb Placement & Explosions</li>
                        <li id="test-power-ups">Power-up System</li>
                        <li id="test-win-conditions">Win Conditions</li>
                        <li id="test-chat-system">Real-time Chat</li>
                    </ul>
                </div>
                <div>
                    <h3>Technical Requirements</h3>
                    <ul class="feature-list" id="technical-features">
                        <li id="test-60fps">60 FPS Performance</li>
                        <li id="test-dom-framework">Custom DOM Framework</li>
                        <li id="test-websocket">WebSocket Integration</li>
                        <li id="test-game-flow">Complete Game Flow</li>
                        <li id="test-responsive">Responsive Design</li>
                        <li id="test-error-handling">Error Handling</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Automated Tests</h2>
            <div id="test-status" class="status info">Ready to run tests</div>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="runPerformanceTest()">Test Performance</button>
            <button onclick="runMultiplayerTest()">Test Multiplayer</button>
            <button onclick="openMultipleWindows()">Open Multiple Game Windows</button>
        </div>

        <div class="test-section">
            <h2>Manual Test Instructions</h2>
            <div class="test-grid">
                <div>
                    <h3>Game Flow Test</h3>
                    <ol>
                        <li>Click "Start Game" from main menu</li>
                        <li>Select a level from the level selection screen</li>
                        <li>Enter a nickname (2+ characters)</li>
                        <li>Wait in lobby for countdown or other players</li>
                        <li>Use chat system to send messages</li>
                        <li>Play the game using WASD/Arrow keys and Space</li>
                        <li>Verify game ends when one player remains</li>
                        <li>Test "Play Again" and "Main Menu" buttons</li>
                    </ol>
                </div>
                <div>
                    <h3>Multiplayer Test</h3>
                    <ol>
                        <li>Open multiple browser windows/tabs</li>
                        <li>Enter different nicknames in each</li>
                        <li>Verify players appear in lobby</li>
                        <li>Test chat between players</li>
                        <li>Start game and verify synchronization</li>
                        <li>Test player movement sync</li>
                        <li>Test bomb placement sync</li>
                        <li>Verify win conditions work for all players</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Performance Metrics</h2>
            <div id="performance-metrics">
                <div>FPS: <span id="fps-counter">--</span></div>
                <div>Frame Time: <span id="frame-time">--</span>ms</div>
                <div>Memory Usage: <span id="memory-usage">--</span>MB</div>
                <div>WebSocket Status: <span id="websocket-status">--</span></div>
            </div>
        </div>

        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let performanceMonitor = null;
        
        function updateTestStatus(testId, status) {
            const element = document.getElementById(testId);
            if (element) {
                element.className = status;
                element.innerHTML = element.innerHTML.replace(/[✓✗⏳]/, '') + 
                    (status === 'pass' ? ' ✓' : status === 'fail' ? ' ✗' : ' ⏳');
            }
        }
        
        function addTestResult(test, status, message) {
            testResults.push({
                test,
                status,
                message,
                timestamp: new Date().toLocaleTimeString()
            });
            updateTestResults();
        }
        
        function updateTestResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                    <small style="float: right;">${result.timestamp}</small>
                </div>`
            ).join('');
        }
        
        async function runAllTests() {
            document.getElementById('test-status').textContent = 'Running comprehensive tests...';
            document.getElementById('test-status').className = 'status info';
            
            // Test server connection
            await testServerConnection();
            
            // Test WebSocket
            await testWebSocketConnection();
            
            // Test game framework
            await testGameFramework();
            
            // Test level system
            await testLevelSystem();
            
            // Test performance
            await testPerformance();
            
            document.getElementById('test-status').textContent = 'All tests completed!';
            document.getElementById('test-status').className = 'status success';
        }
        
        async function testServerConnection() {
            try {
                const response = await fetch('http://localhost:8082/');
                if (response.ok) {
                    updateTestStatus('test-websocket', 'pass');
                    addTestResult('Server Connection', 'success', 'Server is running and accessible');
                } else {
                    updateTestStatus('test-websocket', 'fail');
                    addTestResult('Server Connection', 'error', `HTTP ${response.status}`);
                }
            } catch (error) {
                updateTestStatus('test-websocket', 'fail');
                addTestResult('Server Connection', 'error', error.message);
            }
        }
        
        async function testWebSocketConnection() {
            return new Promise((resolve) => {
                const ws = new WebSocket('ws://localhost:8082');
                
                ws.onopen = function() {
                    updateTestStatus('test-websocket', 'pass');
                    addTestResult('WebSocket Connection', 'success', 'Connection established');
                    ws.close();
                    resolve();
                };
                
                ws.onerror = function() {
                    updateTestStatus('test-websocket', 'fail');
                    addTestResult('WebSocket Connection', 'error', 'Failed to connect');
                    resolve();
                };
                
                setTimeout(() => {
                    if (ws.readyState !== WebSocket.OPEN) {
                        updateTestStatus('test-websocket', 'fail');
                        addTestResult('WebSocket Connection', 'error', 'Connection timeout');
                        ws.close();
                        resolve();
                    }
                }, 5000);
            });
        }
        
        async function testGameFramework() {
            try {
                // Test if framework classes are available
                if (typeof GameFramework !== 'undefined' && 
                    typeof BombermanGame !== 'undefined' &&
                    typeof GameLevels !== 'undefined') {
                    updateTestStatus('test-dom-framework', 'pass');
                    addTestResult('Game Framework', 'success', 'All framework classes loaded');
                } else {
                    updateTestStatus('test-dom-framework', 'fail');
                    addTestResult('Game Framework', 'error', 'Framework classes not found');
                }
            } catch (error) {
                updateTestStatus('test-dom-framework', 'fail');
                addTestResult('Game Framework', 'error', error.message);
            }
        }
        
        async function testLevelSystem() {
            try {
                const levels = new GameLevels();
                const allLevels = levels.getAllLevels();
                
                if (allLevels.length >= 6) {
                    updateTestStatus('test-level-selection', 'pass');
                    addTestResult('Level System', 'success', `${allLevels.length} levels available`);
                } else {
                    updateTestStatus('test-level-selection', 'fail');
                    addTestResult('Level System', 'error', 'Insufficient levels');
                }
            } catch (error) {
                updateTestStatus('test-level-selection', 'fail');
                addTestResult('Level System', 'error', error.message);
            }
        }
        
        async function testPerformance() {
            startPerformanceMonitoring();
            
            setTimeout(() => {
                const fps = parseFloat(document.getElementById('fps-counter').textContent);
                if (fps >= 60) {
                    updateTestStatus('test-60fps', 'pass');
                    addTestResult('Performance', 'success', `${fps} FPS achieved`);
                } else if (fps >= 30) {
                    updateTestStatus('test-60fps', 'warning');
                    addTestResult('Performance', 'warning', `${fps} FPS (below 60 FPS requirement)`);
                } else {
                    updateTestStatus('test-60fps', 'fail');
                    addTestResult('Performance', 'error', `${fps} FPS (poor performance)`);
                }
            }, 3000);
        }
        
        function startPerformanceMonitoring() {
            let frameCount = 0;
            let lastTime = performance.now();
            
            function updateMetrics() {
                const currentTime = performance.now();
                frameCount++;
                
                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                    const frameTime = Math.round((currentTime - lastTime) / frameCount * 100) / 100;
                    
                    document.getElementById('fps-counter').textContent = fps;
                    document.getElementById('frame-time').textContent = frameTime;
                    
                    if (performance.memory) {
                        const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                        document.getElementById('memory-usage').textContent = memoryMB;
                    }
                    
                    frameCount = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(updateMetrics);
            }
            
            updateMetrics();
        }
        
        function runPerformanceTest() {
            addTestResult('Performance Test', 'info', 'Starting performance monitoring...');
            startPerformanceMonitoring();
        }
        
        function runMultiplayerTest() {
            addTestResult('Multiplayer Test', 'info', 'Opening multiple game windows for testing...');
            openMultipleWindows();
        }
        
        function openMultipleWindows() {
            const gameUrl = 'http://localhost:8082/working-game.html';
            
            for (let i = 1; i <= 3; i++) {
                setTimeout(() => {
                    window.open(gameUrl, `player${i}`, 'width=800,height=600');
                    addTestResult('Multiplayer Window', 'success', `Opened game window ${i}`);
                }, i * 1000);
            }
        }
        
        // Auto-start performance monitoring
        window.onload = function() {
            addTestResult('Test Suite', 'success', 'Complete game test suite loaded');
            startPerformanceMonitoring();
            
            // Auto-run basic tests
            setTimeout(() => {
                runAllTests();
            }, 2000);
        };
    </script>
</body>
</html>
