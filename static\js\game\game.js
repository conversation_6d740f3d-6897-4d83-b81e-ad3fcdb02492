/**
 * Main Bomberman Game Class
 */

class BombermanGame {
    constructor() {
        this.framework = new GameFramework();
        this.gameContainer = null;
        this.currentState = 'MENU';
        this.players = new Map();
        this.gameConfig = {
            mapWidth: 15,
            mapHeight: 13,
            cellSize: 40,
            maxPlayers: 4,
            playerLives: 3,
            bombTimer: 3000, // 3 seconds
            powerUpChance: 0.3 // 30% chance for power-up
        };
        
        // WebSocket connection for multiplayer
        this.socket = null;
        this.playerId = null;
        this.isHost = false;
        this.roomCode = null;
        
        // Game state
        this.gameMap = [];
        this.bombs = new Map();
        this.powerUps = new Map();
        this.gameStartTime = 0;
        this.gameEndTime = 0;

        // Multiplayer manager
        this.multiplayerManager = null;
        this.playerNickname = '';

        this.init();
    }
    
    init() {
        this.setupGameContainer();
        this.setupSystems();
        this.setupScenes();
        this.setupEventListeners();
        this.setupMultiplayer();

        // Start with menu scene
        this.framework.switchScene('menu');
        this.framework.start();
    }
    
    setupGameContainer() {
        // Use existing game container or create new one
        this.gameContainer = document.querySelector('.game-container');

        if (!this.gameContainer) {
            // Create main game container if it doesn't exist
            this.gameContainer = Utils.DOM.createElement('div', 'game-container', {
                position: 'relative',
                width: '100vw',
                height: '100vh',
                overflow: 'hidden',
                backgroundColor: '#2c3e50'
            });
            document.body.appendChild(this.gameContainer);
        } else {
            // Clear existing content and apply game styles
            this.gameContainer.innerHTML = '';
            Object.assign(this.gameContainer.style, {
                position: 'relative',
                width: '100vw',
                height: '100vh',
                overflow: 'hidden',
                backgroundColor: '#2c3e50'
            });
        }
    }
    
    setupSystems() {
        // Add core systems to framework
        this.framework.addSystem(new Systems.RenderSystem());
        this.framework.addSystem(new Systems.PhysicsSystem());
        this.framework.addSystem(new Systems.CollisionSystem());
        this.framework.addSystem(new Systems.AnimationSystem());
        this.framework.addSystem(new Systems.TimerSystem());
        this.framework.addSystem(new Systems.InputSystem());
        this.framework.addSystem(new Systems.AudioSystem());
        
        // Get references to systems for easy access
        this.inputSystem = this.framework.systems.find(s => s.name === 'InputSystem');
        this.collisionSystem = this.framework.systems.find(s => s.name === 'CollisionSystem');
        this.audioSystem = this.framework.systems.find(s => s.name === 'AudioSystem');
    }
    
    setupScenes() {
        // Menu Scene
        this.framework.addScene('menu', {
            enter: () => this.enterMenuScene(),
            exit: () => this.exitMenuScene(),
            update: (deltaTime) => this.updateMenuScene(deltaTime),
            render: () => this.renderMenuScene()
        });
        
        // Level Selection Scene
        this.framework.addScene('levelSelect', {
            enter: () => this.enterLevelSelectScene(),
            exit: () => this.exitLevelSelectScene(),
            update: (deltaTime) => this.updateLevelSelectScene(deltaTime),
            render: () => this.renderLevelSelectScene()
        });

        // Nickname Entry Scene
        this.framework.addScene('nickname', {
            enter: () => this.enterNicknameScene(),
            exit: () => this.exitNicknameScene(),
            update: (deltaTime) => this.updateNicknameScene(deltaTime),
            render: () => this.renderNicknameScene()
        });
        
        // Lobby Scene
        this.framework.addScene('lobby', {
            enter: () => this.enterLobbyScene(),
            exit: () => this.exitLobbyScene(),
            update: (deltaTime) => this.updateLobbyScene(deltaTime),
            render: () => this.renderLobbyScene()
        });
        
        // Game Scene
        this.framework.addScene('game', {
            enter: () => this.enterGameScene(),
            exit: () => this.exitGameScene(),
            update: (deltaTime) => this.updateGameScene(deltaTime),
            render: () => this.renderGameScene()
        });
        
        // Game Over Scene
        this.framework.addScene('gameOver', {
            enter: () => this.enterGameOverScene(),
            exit: () => this.exitGameOverScene(),
            update: (deltaTime) => this.updateGameOverScene(deltaTime),
            render: () => this.renderGameOverScene()
        });
    }
    
    setupMultiplayer() {
        // Initialize multiplayer manager
        this.multiplayerManager = new MultiplayerManager(this);

        // Try to connect to WebSocket server
        try {
            this.multiplayerManager.connect();
        } catch (error) {
            console.warn('Failed to connect to multiplayer server:', error);
        }
    }

    setupEventListeners() {
        // Framework events
        this.framework.on('playerJoined', (data) => this.onPlayerJoined(data));
        this.framework.on('playerLeft', (data) => this.onPlayerLeft(data));
        this.framework.on('gameStart', (data) => this.onGameStart(data));
        this.framework.on('gameEnd', (data) => this.onGameEnd(data));
        this.framework.on('bombPlaced', (data) => this.onBombPlaced(data));
        this.framework.on('bombExploded', (data) => this.onBombExploded(data));
        this.framework.on('playerDied', (data) => this.onPlayerDied(data));
        this.framework.on('powerUpCollected', (data) => this.onPowerUpCollected(data));
    }
    
    // Scene implementations (will be expanded in next files)
    enterMenuScene() {
        this.currentState = 'MENU';
        console.log('Entering menu scene');
    }
    
    exitMenuScene() {
        console.log('Exiting menu scene');
    }
    
    updateMenuScene(deltaTime) {
        // Menu update logic
    }
    
    renderMenuScene() {
        // Menu render logic
    }
    
    enterNicknameScene() {
        this.currentState = 'NICKNAME';
        console.log('Entering nickname scene');
    }
    
    exitNicknameScene() {
        console.log('Exiting nickname scene');
    }
    
    updateNicknameScene(deltaTime) {
        // Nickname entry update logic
    }
    
    renderNicknameScene() {
        // Nickname entry render logic
    }
    
    enterLobbyScene() {
        this.currentState = 'LOBBY';
        console.log('Entering lobby scene');
    }
    
    exitLobbyScene() {
        console.log('Exiting lobby scene');
    }
    
    updateLobbyScene(deltaTime) {
        // Lobby update logic
    }
    
    renderLobbyScene() {
        // Lobby render logic
    }
    
    enterGameScene() {
        this.currentState = 'GAME';
        this.gameStartTime = Date.now();
        console.log('Entering game scene');
    }
    
    exitGameScene() {
        console.log('Exiting game scene');
    }
    
    updateGameScene(deltaTime) {
        // Game update logic
    }
    
    renderGameScene() {
        // Game render logic
    }
    
    enterGameOverScene() {
        this.currentState = 'GAME_OVER';
        this.gameEndTime = Date.now();
        console.log('Entering game over scene');
    }
    
    exitGameOverScene() {
        console.log('Exiting game over scene');
    }
    
    updateGameOverScene(deltaTime) {
        // Game over update logic
    }
    
    renderGameOverScene() {
        // Game over render logic
    }
    
    // Event handlers
    onPlayerJoined(data) {
        console.log('Player joined:', data);
    }
    
    onPlayerLeft(data) {
        console.log('Player left:', data);
    }
    
    onGameStart(data) {
        console.log('Game starting:', data);
        this.framework.switchScene('game');
    }
    
    onGameEnd(data) {
        console.log('Game ended:', data);
        this.framework.switchScene('gameOver');
    }
    
    onBombPlaced(data) {
        console.log('Bomb placed:', data);
    }
    
    onBombExploded(data) {
        console.log('Bomb exploded:', data);
    }
    
    onPlayerDied(data) {
        console.log('Player died:', data);
    }
    
    onPowerUpCollected(data) {
        console.log('Power-up collected:', data);
    }
    
    // Utility methods
    getGameContainer() {
        return this.gameContainer;
    }

    getCurrentState() {
        return this.currentState;
    }

    getGameConfig() {
        return this.gameConfig;
    }

    clearGameContainer() {
        if (this.gameContainer) {
            this.gameContainer.innerHTML = '';
        }
    }
    
    // Start the game
    static start() {
        try {
            console.log('Starting BombermanGame...');
            window.game = new BombermanGame();
            console.log('BombermanGame started successfully');
            return window.game;
        } catch (error) {
            console.error('Failed to start BombermanGame:', error);
            throw error;
        }
    }
}

// Export for global access
window.BombermanGame = BombermanGame;
