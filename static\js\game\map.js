/**
 * Game Map System for Bomberman
 */

class GameMap {
    constructor(game) {
        this.game = game;
        this.width = game.gameConfig.mapWidth;
        this.height = game.gameConfig.mapHeight;
        this.cellSize = game.gameConfig.cellSize;
        this.grid = [];
        this.mapContainer = null;
        this.cellElements = new Map();
        
        // Map cell types
        this.CELL_TYPES = {
            EMPTY: 0,
            WALL: 1,
            BLOCK: 2,
            SPAWN: 3
        };
        
        // Spawn positions (corners)
        this.spawnPositions = [
            { x: 1, y: 1 },           // Top-left
            { x: this.width - 2, y: 1 },           // Top-right
            { x: 1, y: this.height - 2 },         // Bottom-left
            { x: this.width - 2, y: this.height - 2 }  // Bottom-right
        ];
        
        this.init();
    }
    
    init() {
        this.generateMap();
        this.createMapDOM();
    }
    
    generateMap() {
        // Initialize empty grid
        this.grid = [];
        for (let y = 0; y < this.height; y++) {
            this.grid[y] = [];
            for (let x = 0; x < this.width; x++) {
                this.grid[y][x] = this.CELL_TYPES.EMPTY;
            }
        }
        
        // Place walls (border and internal pattern)
        this.placeWalls();
        
        // Place spawn areas
        this.placeSpawnAreas();
        
        // Place destructible blocks randomly
        this.placeBlocks();
    }
    
    placeWalls() {
        // Border walls
        for (let x = 0; x < this.width; x++) {
            this.grid[0][x] = this.CELL_TYPES.WALL;
            this.grid[this.height - 1][x] = this.CELL_TYPES.WALL;
        }
        
        for (let y = 0; y < this.height; y++) {
            this.grid[y][0] = this.CELL_TYPES.WALL;
            this.grid[y][this.width - 1] = this.CELL_TYPES.WALL;
        }
        
        // Internal walls (every other cell in a grid pattern)
        for (let y = 2; y < this.height - 1; y += 2) {
            for (let x = 2; x < this.width - 1; x += 2) {
                this.grid[y][x] = this.CELL_TYPES.WALL;
            }
        }
    }
    
    placeSpawnAreas() {
        // Clear spawn areas and surrounding cells
        this.spawnPositions.forEach(spawn => {
            const { x, y } = spawn;
            
            // Clear 3x3 area around spawn point
            for (let dy = -1; dy <= 1; dy++) {
                for (let dx = -1; dx <= 1; dx++) {
                    const nx = x + dx;
                    const ny = y + dy;
                    
                    if (this.isValidPosition(nx, ny)) {
                        this.grid[ny][nx] = this.CELL_TYPES.EMPTY;
                    }
                }
            }
            
            // Mark spawn position
            this.grid[y][x] = this.CELL_TYPES.SPAWN;
        });
    }
    
    placeBlocks() {
        // Place destructible blocks randomly, avoiding walls and spawn areas
        for (let y = 1; y < this.height - 1; y++) {
            for (let x = 1; x < this.width - 1; x++) {
                if (this.grid[y][x] === this.CELL_TYPES.EMPTY) {
                    // Check if this position is too close to spawn areas
                    if (!this.isNearSpawn(x, y)) {
                        // 70% chance to place a block
                        if (Math.random() < 0.7) {
                            this.grid[y][x] = this.CELL_TYPES.BLOCK;
                        }
                    }
                }
            }
        }
    }
    
    isNearSpawn(x, y) {
        return this.spawnPositions.some(spawn => {
            const distance = Math.abs(x - spawn.x) + Math.abs(y - spawn.y);
            return distance <= 2; // Manhattan distance
        });
    }
    
    createMapDOM() {
        // Create map container
        this.mapContainer = Utils.DOM.createElement('div', 'game-map', {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: (this.width * this.cellSize) + 'px',
            height: (this.height * this.cellSize) + 'px',
            backgroundColor: '#2c3e50',
            border: '2px solid #34495e',
            borderRadius: '5px'
        });
        
        // Create grid cells
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                this.createCell(x, y);
            }
        }
        
        this.game.getGameContainer().appendChild(this.mapContainer);
    }
    
    createCell(x, y) {
        const cellType = this.grid[y][x];
        const cellId = `cell-${x}-${y}`;
        
        const cell = Utils.DOM.createElement('div', this.getCellClassName(cellType), {
            position: 'absolute',
            left: (x * this.cellSize) + 'px',
            top: (y * this.cellSize) + 'px',
            width: this.cellSize + 'px',
            height: this.cellSize + 'px',
            boxSizing: 'border-box'
        });
        
        cell.id = cellId;
        cell.dataset.x = x;
        cell.dataset.y = y;
        cell.dataset.type = cellType;
        
        this.cellElements.set(cellId, cell);
        this.mapContainer.appendChild(cell);
    }
    
    getCellClassName(cellType) {
        switch (cellType) {
            case this.CELL_TYPES.WALL:
                return 'map-cell wall';
            case this.CELL_TYPES.BLOCK:
                return 'map-cell block';
            case this.CELL_TYPES.SPAWN:
                return 'map-cell spawn';
            default:
                return 'map-cell empty';
        }
    }
    
    // Map manipulation methods
    destroyBlock(x, y) {
        if (this.isValidPosition(x, y) && this.grid[y][x] === this.CELL_TYPES.BLOCK) {
            this.grid[y][x] = this.CELL_TYPES.EMPTY;
            
            const cellId = `cell-${x}-${y}`;
            const cell = this.cellElements.get(cellId);
            if (cell) {
                cell.className = this.getCellClassName(this.CELL_TYPES.EMPTY);
                cell.dataset.type = this.CELL_TYPES.EMPTY;
            }
            
            // Chance to spawn power-up
            if (Math.random() < this.game.gameConfig.powerUpChance) {
                this.spawnPowerUp(x, y);
            }
            
            return true;
        }
        return false;
    }
    
    spawnPowerUp(x, y) {
        const powerUpTypes = ['bombs', 'flames', 'speed'];
        const powerUpType = Utils.Array.randomElement(powerUpTypes);
        
        // Create power-up entity
        const powerUpId = `powerup-${x}-${y}`;
        const powerUp = this.game.framework.createEntity(powerUpId);
        
        // Add components
        this.game.framework.addComponent(powerUpId, 'Transform', 
            new Components.Transform(
                x * this.cellSize,
                y * this.cellSize
            )
        );
        
        const powerUpElement = Utils.DOM.createElement('div', `powerup ${powerUpType}`, {
            position: 'absolute',
            width: this.cellSize + 'px',
            height: this.cellSize + 'px',
            backgroundColor: this.getPowerUpColor(powerUpType),
            border: '2px solid white',
            borderRadius: '5px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            fontWeight: 'bold',
            color: 'white',
            textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
            animation: 'powerUpPulse 1s infinite alternate'
        });
        
        powerUpElement.textContent = this.getPowerUpSymbol(powerUpType);
        this.mapContainer.appendChild(powerUpElement);
        
        this.game.framework.addComponent(powerUpId, 'DOMRenderer', 
            new Components.DOMRenderer(powerUpElement, `powerup ${powerUpType}`)
        );
        
        this.game.framework.addComponent(powerUpId, 'Collider',
            new Components.Collider(this.cellSize, this.cellSize, 0, 0)
        );
        
        // Add power-up data
        powerUpElement.dataset.powerUpType = powerUpType;
        powerUpElement.dataset.x = x;
        powerUpElement.dataset.y = y;
        
        // Store in game power-ups map
        this.game.powerUps.set(powerUpId, {
            type: powerUpType,
            x: x,
            y: y,
            element: powerUpElement
        });
    }
    
    getPowerUpColor(type) {
        switch (type) {
            case 'bombs': return '#e74c3c';
            case 'flames': return '#f39c12';
            case 'speed': return '#2ecc71';
            default: return '#9b59b6';
        }
    }
    
    getPowerUpSymbol(type) {
        switch (type) {
            case 'bombs': return '💣';
            case 'flames': return '🔥';
            case 'speed': return '⚡';
            default: return '?';
        }
    }
    
    // Collision detection methods
    isValidPosition(x, y) {
        return x >= 0 && x < this.width && y >= 0 && y < this.height;
    }
    
    isWalkable(x, y) {
        if (!this.isValidPosition(x, y)) return false;
        const cellType = this.grid[y][x];
        return cellType === this.CELL_TYPES.EMPTY || cellType === this.CELL_TYPES.SPAWN;
    }
    
    isDestructible(x, y) {
        if (!this.isValidPosition(x, y)) return false;
        return this.grid[y][x] === this.CELL_TYPES.BLOCK;
    }
    
    isWall(x, y) {
        if (!this.isValidPosition(x, y)) return false;
        return this.grid[y][x] === this.CELL_TYPES.WALL;
    }
    
    getCellType(x, y) {
        if (!this.isValidPosition(x, y)) return null;
        return this.grid[y][x];
    }
    
    // Convert pixel coordinates to grid coordinates
    pixelToGrid(pixelX, pixelY) {
        return {
            x: Math.floor(pixelX / this.cellSize),
            y: Math.floor(pixelY / this.cellSize)
        };
    }
    
    // Convert grid coordinates to pixel coordinates
    gridToPixel(gridX, gridY) {
        return {
            x: gridX * this.cellSize,
            y: gridY * this.cellSize
        };
    }
    
    // Get spawn position for player
    getSpawnPosition(playerIndex) {
        if (playerIndex < this.spawnPositions.length) {
            const spawn = this.spawnPositions[playerIndex];
            return this.gridToPixel(spawn.x, spawn.y);
        }
        return this.gridToPixel(1, 1); // Default spawn
    }
    
    // Get all spawn positions
    getAllSpawnPositions() {
        return this.spawnPositions.map(spawn => this.gridToPixel(spawn.x, spawn.y));
    }
    
    // Cleanup
    destroy() {
        if (this.mapContainer && this.mapContainer.parentNode) {
            this.mapContainer.parentNode.removeChild(this.mapContainer);
        }
        this.cellElements.clear();
        this.mapContainer = null;
    }
    
    // Get map bounds for collision detection
    getMapBounds() {
        return {
            left: 0,
            top: 0,
            right: this.width * this.cellSize,
            bottom: this.height * this.cellSize
        };
    }
}

// Export for use
window.GameMap = GameMap;
