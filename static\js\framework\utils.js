/**
 * Utility functions for the game framework
 */

// Math utilities
const MathUtils = {
    // Linear interpolation
    lerp(a, b, t) {
        return a + (b - a) * t;
    },
    
    // Clamp value between min and max
    clamp(value, min, max) {
        return Math.max(min, Math.min(max, value));
    },
    
    // Distance between two points
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },
    
    // Angle between two points in radians
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },
    
    // Convert degrees to radians
    toRadians(degrees) {
        return degrees * Math.PI / 180;
    },
    
    // Convert radians to degrees
    toDegrees(radians) {
        return radians * 180 / Math.PI;
    },
    
    // Random number between min and max
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    // Random integer between min and max (inclusive)
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // Check if point is inside rectangle
    pointInRect(px, py, rx, ry, rw, rh) {
        return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
    },
    
    // Check if two rectangles overlap
    rectOverlap(r1x, r1y, r1w, r1h, r2x, r2y, r2w, r2h) {
        return !(r1x + r1w <= r2x || r2x + r2w <= r1x || r1y + r1h <= r2y || r2y + r2h <= r1y);
    }
};

// DOM utilities
const DOMUtils = {
    // Create element with classes and styles
    createElement(tag, className = '', styles = {}) {
        const element = document.createElement(tag);
        if (className) element.className = className;
        Object.assign(element.style, styles);
        return element;
    },
    
    // Remove element from DOM
    removeElement(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },
    
    // Get element position relative to viewport
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
        };
    },
    
    // Set element position
    setElementPosition(element, x, y) {
        element.style.left = x + 'px';
        element.style.top = y + 'px';
    },
    
    // Add CSS class
    addClass(element, className) {
        element.classList.add(className);
    },
    
    // Remove CSS class
    removeClass(element, className) {
        element.classList.remove(className);
    },
    
    // Toggle CSS class
    toggleClass(element, className) {
        element.classList.toggle(className);
    },
    
    // Check if element has class
    hasClass(element, className) {
        return element.classList.contains(className);
    }
};

// Color utilities
const ColorUtils = {
    // Convert RGB to hex
    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    },
    
    // Convert hex to RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },
    
    // Interpolate between two colors
    lerpColor(color1, color2, t) {
        const c1 = this.hexToRgb(color1);
        const c2 = this.hexToRgb(color2);
        
        if (!c1 || !c2) return color1;
        
        const r = Math.round(MathUtils.lerp(c1.r, c2.r, t));
        const g = Math.round(MathUtils.lerp(c1.g, c2.g, t));
        const b = Math.round(MathUtils.lerp(c1.b, c2.b, t));
        
        return this.rgbToHex(r, g, b);
    }
};

// Array utilities
const ArrayUtils = {
    // Shuffle array in place
    shuffle(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    },
    
    // Get random element from array
    randomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    },
    
    // Remove element from array
    removeElement(array, element) {
        const index = array.indexOf(element);
        if (index > -1) {
            array.splice(index, 1);
        }
        return array;
    },
    
    // Remove element at index
    removeAt(array, index) {
        if (index >= 0 && index < array.length) {
            array.splice(index, 1);
        }
        return array;
    },
    
    // Check if array contains element
    contains(array, element) {
        return array.indexOf(element) !== -1;
    }
};

// Storage utilities
const StorageUtils = {
    // Save data to localStorage
    save(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (e) {
            console.warn('Failed to save to localStorage:', e);
            return false;
        }
    },
    
    // Load data from localStorage
    load(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.warn('Failed to load from localStorage:', e);
            return defaultValue;
        }
    },
    
    // Remove data from localStorage
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.warn('Failed to remove from localStorage:', e);
            return false;
        }
    },
    
    // Clear all localStorage
    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            console.warn('Failed to clear localStorage:', e);
            return false;
        }
    }
};

// Performance utilities
const PerformanceUtils = {
    // Debounce function calls
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function calls
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Measure function execution time
    measureTime(func, name = 'Function') {
        const start = performance.now();
        const result = func();
        const end = performance.now();
        console.log(`${name} took ${end - start} milliseconds`);
        return result;
    },
    
    // Object pool for reusing objects
    createObjectPool(createFn, resetFn, initialSize = 10) {
        const pool = [];
        
        // Pre-populate pool
        for (let i = 0; i < initialSize; i++) {
            pool.push(createFn());
        }
        
        return {
            get() {
                return pool.length > 0 ? pool.pop() : createFn();
            },
            
            release(obj) {
                if (resetFn) resetFn(obj);
                pool.push(obj);
            },
            
            size() {
                return pool.length;
            }
        };
    }
};

// Event utilities
const EventUtils = {
    // Create custom event
    createEvent(name, detail = null) {
        return new CustomEvent(name, { detail });
    },
    
    // Dispatch custom event
    dispatchEvent(element, eventName, detail = null) {
        const event = this.createEvent(eventName, detail);
        element.dispatchEvent(event);
    },
    
    // Add event listener with automatic cleanup
    addListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        
        return () => {
            element.removeEventListener(event, handler, options);
        };
    }
};

// Export utilities
window.Utils = {
    Math: MathUtils,
    DOM: DOMUtils,
    Color: ColorUtils,
    Array: ArrayUtils,
    Storage: StorageUtils,
    Performance: PerformanceUtils,
    Event: EventUtils
};
