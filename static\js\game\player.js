/**
 * Player System for Bomberman
 */

class Player {
    constructor(game, playerId, playerIndex, nickname = '') {
        this.game = game;
        this.playerId = playerId;
        this.playerIndex = playerIndex;
        this.nickname = nickname;
        
        // Player stats
        this.lives = game.gameConfig.playerLives;
        this.isAlive = true;
        this.isInvulnerable = false;
        this.invulnerabilityTime = 0;
        this.invulnerabilityDuration = 2000; // 2 seconds
        
        // Movement properties
        this.speed = 150; // pixels per second
        this.gridX = 0;
        this.gridY = 0;
        this.targetX = 0;
        this.targetY = 0;
        this.isMoving = false;
        this.direction = 'down';
        
        // Power-ups
        this.maxBombs = 1;
        this.bombRange = 1;
        this.speedMultiplier = 1;
        this.activeBombs = 0;
        
        // Special abilities
        this.canPassThroughBombs = false;
        this.canPassThroughBlocks = false;
        this.hasDetonator = false;
        
        // Visual elements
        this.element = null;
        this.entityId = `player-${playerId}`;
        
        this.init();
    }
    
    init() {
        this.createPlayerEntity();
        this.setupInputHandlers();
        this.setSpawnPosition();
    }
    
    createPlayerEntity() {
        // Create player entity in framework
        const entity = this.game.framework.createEntity(this.entityId);
        
        // Create DOM element
        this.element = Utils.DOM.createElement('div', `player player-${this.playerIndex + 1}`, {
            position: 'absolute',
            width: (this.game.gameConfig.cellSize - 4) + 'px',
            height: (this.game.gameConfig.cellSize - 4) + 'px',
            borderRadius: '50%',
            zIndex: '20',
            transition: 'transform 0.1s ease'
        });
        
        // Add player number indicator
        const indicator = Utils.DOM.createElement('div', 'player-indicator', {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '16px',
            textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
        });
        indicator.textContent = this.playerIndex + 1;
        this.element.appendChild(indicator);
        
        // Add to game map
        this.game.gameMap.mapContainer.appendChild(this.element);
        
        // Add components to entity
        this.game.framework.addComponent(this.entityId, 'Transform', 
            new Components.Transform(0, 0)
        );
        
        this.game.framework.addComponent(this.entityId, 'DOMRenderer', 
            new Components.DOMRenderer(this.element, `player player-${this.playerIndex + 1}`)
        );
        
        this.game.framework.addComponent(this.entityId, 'Physics', 
            new Components.Physics(0, 0, 1)
        );
        
        this.game.framework.addComponent(this.entityId, 'Collider',
            new Components.Collider(
                this.game.gameConfig.cellSize - 8,
                this.game.gameConfig.cellSize - 8,
                4, 4
            )
        );
        
        // Setup collision callbacks
        const collisionSystem = this.game.framework.getSystem('CollisionSystem');
        if (collisionSystem) {
            collisionSystem.onCollision(this.entityId, (otherEntity, otherCollider) => {
                this.handleCollision(otherEntity, otherCollider);
            });
        }
    }
    
    setupInputHandlers() {
        // Only setup input for local player (index 0 for now)
        if (this.playerIndex === 0) {
            const inputSystem = this.game.framework.getSystem('InputSystem');
            if (inputSystem) {
                inputSystem.addInputHandler(this.entityId, (input, entity, deltaTime) => {
                    this.handleInput(input, deltaTime);
                });
            }
        }
    }
    
    setSpawnPosition() {
        const spawnPos = this.game.gameMap.getSpawnPosition(this.playerIndex);
        this.setPosition(spawnPos.x + 4, spawnPos.y + 4); // Center in cell with some padding
        this.gridX = Math.floor((spawnPos.x + this.game.gameConfig.cellSize / 2) / this.game.gameConfig.cellSize);
        this.gridY = Math.floor((spawnPos.y + this.game.gameConfig.cellSize / 2) / this.game.gameConfig.cellSize);
    }
    
    setPosition(x, y) {
        const transform = this.game.framework.getComponent(this.entityId, 'Transform');
        if (transform) {
            transform.setPosition(x, y);
        }
    }
    
    getPosition() {
        const transform = this.game.framework.getComponent(this.entityId, 'Transform');
        return transform ? { x: transform.x, y: transform.y } : { x: 0, y: 0 };
    }
    
    handleInput(input, deltaTime) {
        if (!this.isAlive) return;
        
        const physics = this.game.framework.getComponent(this.entityId, 'Physics');
        if (!physics) return;
        
        let moveX = 0;
        let moveY = 0;
        let newDirection = this.direction;
        
        // Movement input
        if (input.isKeyPressed('ArrowUp') || input.isKeyPressed('KeyW')) {
            moveY = -1;
            newDirection = 'up';
        } else if (input.isKeyPressed('ArrowDown') || input.isKeyPressed('KeyS')) {
            moveY = 1;
            newDirection = 'down';
        } else if (input.isKeyPressed('ArrowLeft') || input.isKeyPressed('KeyA')) {
            moveX = -1;
            newDirection = 'left';
        } else if (input.isKeyPressed('ArrowRight') || input.isKeyPressed('KeyD')) {
            moveX = 1;
            newDirection = 'right';
        }
        
        // Update direction
        this.direction = newDirection;
        
        // Apply movement
        if (moveX !== 0 || moveY !== 0) {
            const currentPos = this.getPosition();
            const newX = currentPos.x + moveX * this.speed * this.speedMultiplier * deltaTime;
            const newY = currentPos.y + moveY * this.speed * this.speedMultiplier * deltaTime;
            
            // Check collision with map
            if (this.canMoveTo(newX, newY)) {
                physics.velocityX = moveX * this.speed * this.speedMultiplier;
                physics.velocityY = moveY * this.speed * this.speedMultiplier;
                this.isMoving = true;
            } else {
                physics.velocityX = 0;
                physics.velocityY = 0;
                this.isMoving = false;
            }
        } else {
            physics.velocityX = 0;
            physics.velocityY = 0;
            this.isMoving = false;
        }
        
        // Bomb placement
        if (input.isKeyPressed('Space') && this.canPlaceBomb()) {
            this.placeBomb();
        }
        
        // Update grid position
        this.updateGridPosition();

        // Send movement to multiplayer (only for local player)
        if (this.playerIndex === 0 && this.game.multiplayerManager && this.isMoving) {
            const pos = this.getPosition();
            this.game.multiplayerManager.sendPlayerMove(pos.x, pos.y, this.direction);
        }
    }
    
    canMoveTo(x, y) {
        const cellSize = this.game.gameConfig.cellSize;
        const playerSize = cellSize - 8;
        
        // Check corners of player bounding box
        const corners = [
            { x: x, y: y },
            { x: x + playerSize, y: y },
            { x: x, y: y + playerSize },
            { x: x + playerSize, y: y + playerSize }
        ];
        
        for (const corner of corners) {
            const gridPos = this.game.gameMap.pixelToGrid(corner.x, corner.y);
            
            if (!this.game.gameMap.isWalkable(gridPos.x, gridPos.y)) {
                // Check for special abilities
                if (this.game.gameMap.isDestructible(gridPos.x, gridPos.y) && this.canPassThroughBlocks) {
                    continue;
                }
                return false;
            }
        }
        
        return true;
    }
    
    updateGridPosition() {
        const pos = this.getPosition();
        const cellSize = this.game.gameConfig.cellSize;
        this.gridX = Math.floor((pos.x + cellSize / 2) / cellSize);
        this.gridY = Math.floor((pos.y + cellSize / 2) / cellSize);
    }
    
    canPlaceBomb() {
        return this.isAlive && 
               this.activeBombs < this.maxBombs && 
               !this.game.bombs.has(`bomb-${this.gridX}-${this.gridY}`);
    }
    
    placeBomb() {
        const bombId = `bomb-${this.gridX}-${this.gridY}`;
        const bomb = new Bomb(this.game, bombId, this.gridX, this.gridY, this.bombRange, this);
        
        this.game.bombs.set(bombId, bomb);
        this.activeBombs++;
        
        // Notify multiplayer
        if (this.game.multiplayerManager) {
            this.game.multiplayerManager.sendBombPlace(this.gridX, this.gridY);
        }
        
        // Emit event
        this.game.framework.emit('bombPlaced', {
            playerId: this.playerId,
            x: this.gridX,
            y: this.gridY,
            range: this.bombRange
        });
    }
    
    handleCollision(otherEntity, otherCollider) {
        // Check collision with power-ups
        if (otherEntity.id.startsWith('powerup-')) {
            this.collectPowerUp(otherEntity);
        }
        
        // Check collision with explosions
        if (otherEntity.id.startsWith('explosion-')) {
            this.takeDamage();
        }
        
        // Check collision with other players (for team modes)
        if (otherEntity.id.startsWith('player-') && otherEntity.id !== this.entityId) {
            // Handle player-to-player collision
        }
    }
    
    collectPowerUp(powerUpEntity) {
        const powerUpData = this.game.powerUps.get(powerUpEntity.id);
        if (!powerUpData) return;
        
        // Apply power-up effect
        switch (powerUpData.type) {
            case 'bombs':
                this.maxBombs++;
                break;
            case 'flames':
                this.bombRange++;
                break;
            case 'speed':
                this.speedMultiplier += 0.2;
                break;
        }
        
        // Remove power-up
        this.game.framework.removeEntity(powerUpEntity.id);
        this.game.powerUps.delete(powerUpEntity.id);
        
        if (powerUpData.element && powerUpData.element.parentNode) {
            powerUpData.element.parentNode.removeChild(powerUpData.element);
        }
        
        // Notify multiplayer
        if (this.game.multiplayerManager) {
            this.game.multiplayerManager.sendMessage('powerUpCollected', {
                playerId: this.playerId,
                powerUpType: powerUpData.type,
                x: powerUpData.x,
                y: powerUpData.y
            });
        }
        
        // Emit event
        this.game.framework.emit('powerUpCollected', {
            playerId: this.playerId,
            powerUpType: powerUpData.type
        });
    }
    
    takeDamage() {
        if (!this.isAlive || this.isInvulnerable) return;
        
        this.lives--;
        
        if (this.lives <= 0) {
            this.die();
        } else {
            this.makeInvulnerable();
        }
        
        // Emit event
        this.game.framework.emit('playerDamaged', {
            playerId: this.playerId,
            lives: this.lives
        });
    }
    
    makeInvulnerable() {
        this.isInvulnerable = true;
        this.invulnerabilityTime = this.invulnerabilityDuration;
        
        // Visual feedback
        this.element.style.opacity = '0.5';
        this.element.style.animation = 'playerFlash 0.2s infinite';
    }
    
    die() {
        this.isAlive = false;
        this.element.style.display = 'none';
        
        // Drop power-up on death (bonus feature)
        if (Math.random() < 0.5) {
            this.dropPowerUp();
        }
        
        // Notify multiplayer
        if (this.game.multiplayerManager) {
            this.game.multiplayerManager.sendMessage('playerDied', {
                playerId: this.playerId,
                x: this.gridX,
                y: this.gridY
            });
        }
        
        // Emit event
        this.game.framework.emit('playerDied', {
            playerId: this.playerId
        });
    }
    
    dropPowerUp() {
        // Drop a random power-up at player position
        this.game.gameMap.spawnPowerUp(this.gridX, this.gridY);
    }
    
    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update invulnerability
        if (this.isInvulnerable) {
            this.invulnerabilityTime -= deltaTime * 1000;
            if (this.invulnerabilityTime <= 0) {
                this.isInvulnerable = false;
                this.element.style.opacity = '1';
                this.element.style.animation = '';
            }
        }
    }
    
    // Getters
    getStats() {
        return {
            lives: this.lives,
            maxBombs: this.maxBombs,
            bombRange: this.bombRange,
            speedMultiplier: this.speedMultiplier,
            isAlive: this.isAlive
        };
    }
    
    getGridPosition() {
        return { x: this.gridX, y: this.gridY };
    }
    
    // Cleanup
    destroy() {
        this.game.framework.removeEntity(this.entityId);
        this.game.inputSystem.removeInputHandler(this.entityId);
        this.game.collisionSystem.removeCollisionCallbacks(this.entityId);
        
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
    }
}

// Export for use
window.Player = Player;
