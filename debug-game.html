<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman - Debug</title>
    <link rel="stylesheet" href="./static/style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #07131F;
            color: white;
            font-family: 'EngraversGothic', sans-serif;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            max-width: 500px;
        }
        
        .debug-step {
            margin: 10px 0;
            padding: 5px;
            border-left: 3px solid #3498db;
            padding-left: 10px;
        }
        
        .debug-step.success {
            border-color: #2ecc71;
            background: rgba(46, 204, 113, 0.1);
        }
        
        .debug-step.error {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
        
        .nickname-scene {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #07131F;
        }
        
        .nickname-form {
            background: #2c3e50;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
        }
        
        .nickname-input {
            padding: 15px;
            font-size: 1.2rem;
            border: 2px solid #34495e;
            border-radius: 5px;
            background: #34495e;
            color: white;
            margin: 20px 0;
            width: 300px;
        }
        
        .nickname-button {
            padding: 15px 30px;
            font-size: 1.1rem;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
            margin: 10px;
        }
        
        .nickname-button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div id="debug-info" class="debug-info">
        <h3>Debug Information</h3>
        <div id="debug-steps"></div>
    </div>
    
    <div id="nickname-scene" class="nickname-scene" style="display: none;">
        <div class="nickname-form">
            <h1>Enter Your Nickname</h1>
            <input type="text" id="nickname-input" class="nickname-input" placeholder="Enter nickname..." maxlength="20">
            <br>
            <button id="join-button" class="nickname-button">Join Game</button>
            <button id="back-button" class="nickname-button" style="background: #e74c3c;">Back to Main Menu</button>
        </div>
    </div>

    <script>
        let debugSteps = [];
        
        function addDebugStep(message, success = true) {
            debugSteps.push({ message, success, timestamp: new Date().toLocaleTimeString() });
            updateDebugDisplay();
            console.log(`[${success ? 'SUCCESS' : 'ERROR'}] ${message}`);
        }
        
        function updateDebugDisplay() {
            const container = document.getElementById('debug-steps');
            container.innerHTML = debugSteps.map(step => 
                `<div class="debug-step ${step.success ? 'success' : 'error'}">
                    [${step.timestamp}] ${step.message}
                </div>`
            ).join('');
        }
        
        // Start debugging
        addDebugStep('Page loaded successfully');
        
        document.addEventListener('DOMContentLoaded', function() {
            addDebugStep('DOM Content Loaded');
            
            try {
                // Test if we can access the elements
                const nicknameScene = document.getElementById('nickname-scene');
                const nicknameInput = document.getElementById('nickname-input');
                const joinButton = document.getElementById('join-button');
                const backButton = document.getElementById('back-button');
                
                if (nicknameScene && nicknameInput && joinButton && backButton) {
                    addDebugStep('All DOM elements found');
                    
                    // Show the nickname scene
                    nicknameScene.style.display = 'flex';
                    addDebugStep('Nickname scene displayed');
                    
                    // Focus the input
                    setTimeout(() => {
                        nicknameInput.focus();
                        addDebugStep('Input focused');
                    }, 100);
                    
                    // Add event listeners
                    joinButton.addEventListener('click', function() {
                        const nickname = nicknameInput.value.trim();
                        if (nickname.length >= 2) {
                            addDebugStep(`Nickname entered: ${nickname}`);
                            alert(`Nickname: ${nickname}\nThis would normally proceed to lobby.`);
                        } else {
                            addDebugStep('Nickname too short', false);
                            alert('Nickname must be at least 2 characters long');
                        }
                    });
                    
                    backButton.addEventListener('click', function() {
                        addDebugStep('Back button clicked');
                        window.location.href = '/';
                    });
                    
                    nicknameInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            joinButton.click();
                        }
                    });
                    
                    addDebugStep('Event listeners attached');
                    
                } else {
                    addDebugStep('Some DOM elements not found', false);
                }
                
            } catch (error) {
                addDebugStep(`JavaScript error: ${error.message}`, false);
            }
        });
        
        // Test framework loading
        setTimeout(() => {
            try {
                if (typeof Utils !== 'undefined') {
                    addDebugStep('Utils framework loaded');
                } else {
                    addDebugStep('Utils framework not loaded', false);
                }
                
                if (typeof Components !== 'undefined') {
                    addDebugStep('Components framework loaded');
                } else {
                    addDebugStep('Components framework not loaded', false);
                }
                
                if (typeof GameFramework !== 'undefined') {
                    addDebugStep('GameFramework loaded');
                } else {
                    addDebugStep('GameFramework not loaded', false);
                }
                
                if (typeof BombermanGame !== 'undefined') {
                    addDebugStep('BombermanGame loaded');
                } else {
                    addDebugStep('BombermanGame not loaded', false);
                }
                
            } catch (error) {
                addDebugStep(`Framework test error: ${error.message}`, false);
            }
        }, 1000);
        
        // Hide debug info after 10 seconds
        setTimeout(() => {
            const debugInfo = document.getElementById('debug-info');
            if (debugInfo) {
                debugInfo.style.display = 'none';
            }
        }, 10000);
    </script>
    
    <!-- Framework Scripts -->
    <script src="./static/js/framework/utils.js"></script>
    <script src="./static/js/framework/components.js"></script>
    <script src="./static/js/framework/systems.js"></script>
    <script src="./static/js/framework/core.js"></script>
    
    <!-- Game Scripts -->
    <script src="./static/js/game/multiplayer.js"></script>
    <script src="./static/js/game/map.js"></script>
    <script src="./static/js/game/player.js"></script>
    <script src="./static/js/game/bomb.js"></script>
    <script src="./static/js/game/scenes.js"></script>
    <script src="./static/js/game/gameScene.js"></script>
    <script src="./static/js/game/game.js"></script>
</body>
</html>
