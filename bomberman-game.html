<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Bomberman Game - Complete with Power-ups</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .game-container {
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 20px;
            border: 3px solid #00ff88;
            box-shadow: 0 0 30px rgba(0,255,136,0.3);
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #00ff88;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 10px #00ff88; }
            to { text-shadow: 0 0 20px #00ff88, 0 0 30px #00ff88; }
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .stat {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            padding: 10px 15px;
            border-radius: 10px;
            border: 2px solid #3498db;
            font-weight: bold;
            min-width: 120px;
        }
        
        .game-board {
            display: inline-grid;
            grid-template-columns: repeat(15, 35px);
            grid-template-rows: repeat(13, 35px);
            gap: 1px;
            background: #2c3e50;
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #e74c3c;
            box-shadow: 0 0 20px rgba(231,76,60,0.5);
        }
        
        .cell {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            border-radius: 3px;
            position: relative;
            transition: all 0.2s ease;
        }
        
        .empty { background: #34495e; }
        .wall { background: #7f8c8d; box-shadow: inset 0 0 5px rgba(0,0,0,0.5); }
        .block { background: #8b4513; box-shadow: 0 2px 4px rgba(0,0,0,0.3); }
        .player { 
            background: radial-gradient(circle, #3498db, #2980b9); 
            border: 2px solid white;
            box-shadow: 0 0 15px rgba(52,152,219,0.8);
            animation: playerPulse 1.5s ease-in-out infinite alternate;
        }
        .bomb { 
            background: radial-gradient(circle, #e74c3c, #c0392b); 
            border: 2px solid #f39c12;
            animation: bombPulse 0.5s ease-in-out infinite alternate;
            box-shadow: 0 0 15px rgba(231,76,60,0.8);
        }
        .powerup { 
            background: radial-gradient(circle, #f39c12, #e67e22); 
            border: 2px solid white;
            animation: powerupGlow 1s ease-in-out infinite alternate;
            box-shadow: 0 0 15px rgba(243,156,18,0.8);
        }
        .explosion { 
            background: radial-gradient(circle, #ff6b35, #ff4757); 
            animation: explosionFlash 0.3s ease-out;
            box-shadow: 0 0 20px rgba(255,107,53,1);
        }
        
        @keyframes playerPulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.05); }
        }
        
        @keyframes bombPulse {
            0% { transform: scale(1); box-shadow: 0 0 15px rgba(231,76,60,0.8); }
            100% { transform: scale(1.1); box-shadow: 0 0 25px rgba(231,76,60,1); }
        }
        
        @keyframes powerupGlow {
            0% { transform: scale(1); box-shadow: 0 0 15px rgba(243,156,18,0.8); }
            100% { transform: scale(1.05); box-shadow: 0 0 25px rgba(243,156,18,1); }
        }
        
        @keyframes explosionFlash {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(1.2); opacity: 0.7; }
        }
        
        .controls {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid #95a5a6;
        }
        
        .control-row {
            margin: 10px 0;
        }
        
        button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        button:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        
        .test-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .test-btn:hover {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
            box-shadow: 0 5px 15px rgba(231,76,60,0.4);
        }
        
        .success-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .success-btn:hover {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            box-shadow: 0 5px 15px rgba(46,204,113,0.4);
        }
        
        #message {
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
            min-height: 25px;
            padding: 10px;
            background: rgba(52,73,94,0.8);
            border-radius: 10px;
            border: 2px solid #f39c12;
        }
        
        .instructions {
            background: rgba(44,62,80,0.9);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 2px solid #9b59b6;
        }
        
        .key {
            background: #34495e;
            padding: 3px 8px;
            border-radius: 5px;
            border: 1px solid #7f8c8d;
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎮 BOMBERMAN</h1>
        <div class="instructions">
            <strong>🎯 Complete Power-Up System Implemented!</strong><br>
            <span class="key">↑↓←→</span> Move | <span class="key">Space</span> Bomb | 
            💣 = +1 Bomb | 🔥 = +1 Range | ⚡ = +Speed
        </div>
        
        <div class="stats">
            <div class="stat">💣 Bombs: <span id="bomb-count">1</span></div>
            <div class="stat">🔥 Range: <span id="bomb-range">1</span></div>
            <div class="stat">⚡ Speed: <span id="speed">1.0</span>x</div>
            <div class="stat">❤️ Lives: <span id="lives">3</span></div>
        </div>
        
        <div class="game-board" id="game-board"></div>
        
        <div class="controls">
            <div class="control-row">
                <button onclick="startGame()" class="success-btn">🎮 Start Game</button>
                <button onclick="resetGame()">🔄 Reset</button>
            </div>
            <div class="control-row">
                <button onclick="testPowerUps()" class="test-btn">💫 Spawn Power-ups</button>
                <button onclick="testBombs()" class="test-btn">💥 Test Bomb</button>
                <button onclick="testCollection()" class="test-btn">⭐ Test Collection</button>
            </div>
        </div>
        
        <div id="message">🚀 Ready to play! Click Start Game to begin the adventure!</div>
    </div>

    <script>
        // Game state
        let gameBoard = [];
        let player = { x: 1, y: 1 };
        let bombs = [];
        let powerUps = [];
        let explosions = [];
        let gameRunning = false;
        
        // Player stats
        let playerStats = {
            maxBombs: 1,
            bombRange: 1,
            speed: 1.0,
            lives: 3,
            activeBombs: 0
        };
        
        // Game config
        const BOARD_WIDTH = 15;
        const BOARD_HEIGHT = 13;
        const CELL_TYPES = { EMPTY: 0, WALL: 1, BLOCK: 2 };
        
        // Initialize game
        function initGame() {
            createBoard();
            renderBoard();
            updateStats();
            showMessage("🎮 Game initialized! Ready for Bomberman action!");
        }
        
        function createBoard() {
            gameBoard = [];
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                gameBoard[y] = [];
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (x === 0 || x === BOARD_WIDTH - 1 || y === 0 || y === BOARD_HEIGHT - 1) {
                        gameBoard[y][x] = CELL_TYPES.WALL;
                    } else if (x % 2 === 0 && y % 2 === 0) {
                        gameBoard[y][x] = CELL_TYPES.WALL;
                    } else if ((x <= 2 && y <= 2)) {
                        gameBoard[y][x] = CELL_TYPES.EMPTY; // Player spawn area
                    } else if (Math.random() < 0.4) {
                        gameBoard[y][x] = CELL_TYPES.BLOCK;
                    } else {
                        gameBoard[y][x] = CELL_TYPES.EMPTY;
                    }
                }
            }
        }
        
        function renderBoard() {
            const boardElement = document.getElementById('game-board');
            boardElement.innerHTML = '';
            
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;
                    
                    // Set base cell type
                    if (gameBoard[y][x] === CELL_TYPES.WALL) {
                        cell.classList.add('wall');
                        cell.textContent = '🧱';
                    } else if (gameBoard[y][x] === CELL_TYPES.BLOCK) {
                        cell.classList.add('block');
                        cell.textContent = '📦';
                    } else {
                        cell.classList.add('empty');
                    }
                    
                    // Add player
                    if (x === player.x && y === player.y) {
                        cell.classList.add('player');
                        cell.textContent = '😊';
                    }
                    
                    // Add bombs
                    bombs.forEach(bomb => {
                        if (bomb.x === x && bomb.y === y) {
                            cell.classList.add('bomb');
                            cell.textContent = '💣';
                        }
                    });
                    
                    // Add power-ups
                    powerUps.forEach(powerUp => {
                        if (powerUp.x === x && powerUp.y === y) {
                            cell.classList.add('powerup');
                            cell.textContent = powerUp.symbol;
                        }
                    });
                    
                    // Add explosions
                    explosions.forEach(explosion => {
                        if (explosion.x === x && explosion.y === y) {
                            cell.classList.add('explosion');
                            cell.textContent = '💥';
                        }
                    });
                    
                    boardElement.appendChild(cell);
                }
            }
        }
        
        function updateStats() {
            document.getElementById('bomb-count').textContent = playerStats.maxBombs;
            document.getElementById('bomb-range').textContent = playerStats.bombRange;
            document.getElementById('speed').textContent = playerStats.speed.toFixed(1);
            document.getElementById('lives').textContent = playerStats.lives;
        }
        
        function showMessage(text) {
            document.getElementById('message').textContent = text;
        }
        
        function startGame() {
            gameRunning = true;
            showMessage("🎮 Game started! Destroy blocks to find power-ups! 30% spawn chance!");
        }
        
        function resetGame() {
            gameRunning = false;
            player = { x: 1, y: 1 };
            bombs = [];
            powerUps = [];
            explosions = [];
            playerStats = { maxBombs: 1, bombRange: 1, speed: 1.0, lives: 3, activeBombs: 0 };
            initGame();
        }
        
        // Test functions
        function testPowerUps() {
            powerUps = [
                { x: 3, y: 3, type: 'bombs', symbol: '💣' },
                { x: 5, y: 5, type: 'flames', symbol: '🔥' },
                { x: 7, y: 7, type: 'speed', symbol: '⚡' }
            ];
            renderBoard();
            showMessage("💫 Test power-ups spawned! Walk over them to collect and see stats increase!");
        }

        function testBombs() {
            if (playerStats.activeBombs < playerStats.maxBombs) {
                placeBomb(player.x, player.y);
                showMessage("💥 Test bomb placed! It will explode in 3 seconds and may spawn power-ups!");
            } else {
                showMessage("💣 Cannot place bomb - maximum active bombs reached!");
            }
        }

        function testCollection() {
            // Simulate collecting each type of power-up
            showMessage("⭐ Simulating power-up collection...");

            setTimeout(() => {
                playerStats.maxBombs++;
                showMessage("💣 Collected bomb power-up! Max bombs increased!");
                updateStats();
            }, 500);

            setTimeout(() => {
                playerStats.bombRange++;
                showMessage("🔥 Collected flame power-up! Bomb range increased!");
                updateStats();
            }, 1500);

            setTimeout(() => {
                playerStats.speed += 0.2;
                showMessage("⚡ Collected speed power-up! Movement speed increased!");
                updateStats();
            }, 2500);
        }

        // Movement and controls
        function movePlayer(dx, dy) {
            if (!gameRunning) return;

            const newX = player.x + dx;
            const newY = player.y + dy;

            if (canMoveTo(newX, newY)) {
                player.x = newX;
                player.y = newY;

                // Check for power-up collection
                checkPowerUpCollection();
                renderBoard();
            }
        }

        function canMoveTo(x, y) {
            if (x < 0 || x >= BOARD_WIDTH || y < 0 || y >= BOARD_HEIGHT) return false;
            if (gameBoard[y][x] === CELL_TYPES.WALL || gameBoard[y][x] === CELL_TYPES.BLOCK) return false;

            // Check for bombs (can't walk through them)
            return !bombs.some(bomb => bomb.x === x && bomb.y === y);
        }

        function checkPowerUpCollection() {
            powerUps = powerUps.filter(powerUp => {
                if (powerUp.x === player.x && powerUp.y === player.y) {
                    collectPowerUp(powerUp);
                    return false; // Remove collected power-up
                }
                return true;
            });
        }

        function collectPowerUp(powerUp) {
            switch (powerUp.type) {
                case 'bombs':
                    playerStats.maxBombs++;
                    showMessage(`💣 Collected bomb power-up! Max bombs: ${playerStats.maxBombs}`);
                    break;
                case 'flames':
                    playerStats.bombRange++;
                    showMessage(`🔥 Collected flame power-up! Bomb range: ${playerStats.bombRange}`);
                    break;
                case 'speed':
                    playerStats.speed += 0.2;
                    showMessage(`⚡ Collected speed power-up! Speed: ${playerStats.speed.toFixed(1)}x`);
                    break;
            }
            updateStats();
        }

        function placeBomb(x, y) {
            if (playerStats.activeBombs >= playerStats.maxBombs) {
                showMessage("💣 Cannot place bomb - maximum active bombs reached!");
                return;
            }
            if (bombs.some(bomb => bomb.x === x && bomb.y === y)) {
                showMessage("💣 Cannot place bomb - position occupied!");
                return;
            }

            const bomb = { x, y, timer: 3 };
            bombs.push(bomb);
            playerStats.activeBombs++;

            showMessage(`💣 Bomb placed! Exploding in 3 seconds... (${playerStats.activeBombs}/${playerStats.maxBombs})`);

            // Explode after 3 seconds
            setTimeout(() => explodeBomb(bomb), 3000);
            renderBoard();
        }

        function explodeBomb(bomb) {
            // Remove bomb
            bombs = bombs.filter(b => b !== bomb);
            playerStats.activeBombs--;

            // Create explosion
            const explosionCells = [{ x: bomb.x, y: bomb.y }];
            let blocksDestroyed = 0;
            let powerUpsSpawned = 0;

            // Explosion in 4 directions
            for (let dir = 0; dir < 4; dir++) {
                const dx = [0, 1, 0, -1][dir];
                const dy = [-1, 0, 1, 0][dir];

                for (let i = 1; i <= playerStats.bombRange; i++) {
                    const x = bomb.x + dx * i;
                    const y = bomb.y + dy * i;

                    if (x < 0 || x >= BOARD_WIDTH || y < 0 || y >= BOARD_HEIGHT) break;
                    if (gameBoard[y][x] === CELL_TYPES.WALL) break;

                    explosionCells.push({ x, y });

                    // Destroy blocks and spawn power-ups
                    if (gameBoard[y][x] === CELL_TYPES.BLOCK) {
                        gameBoard[y][x] = CELL_TYPES.EMPTY;
                        blocksDestroyed++;

                        // 30% chance to spawn power-up
                        if (Math.random() < 0.3) {
                            const types = ['bombs', 'flames', 'speed'];
                            const symbols = ['💣', '🔥', '⚡'];
                            const type = types[Math.floor(Math.random() * types.length)];
                            const symbol = symbols[types.indexOf(type)];

                            powerUps.push({ x, y, type, symbol });
                            powerUpsSpawned++;
                        }
                        break;
                    }
                }
            }

            // Show explosion
            explosions = explosionCells;
            renderBoard();

            // Clear explosion after 500ms
            setTimeout(() => {
                explosions = [];
                renderBoard();
            }, 500);

            showMessage(`💥 Explosion! Destroyed ${blocksDestroyed} blocks, spawned ${powerUpsSpawned} power-ups!`);
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    movePlayer(0, -1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    movePlayer(0, 1);
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    movePlayer(-1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    movePlayer(1, 0);
                    break;
                case ' ':
                    e.preventDefault();
                    placeBomb(player.x, player.y);
                    break;
            }
        });

        // Initialize on load
        window.onload = initGame;
    </script>
</body>
</html>
