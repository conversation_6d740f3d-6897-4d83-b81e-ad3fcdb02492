<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2c3e50;
            color: white;
        }
        .container {
            background: #34495e;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: none;
            border-radius: 5px;
        }
        button {
            background: #3498db;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        #messages {
            background: #2c3e50;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid #34495e;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 3px;
        }
        .system {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #2ecc71;
        }
    </style>
</head>
<body>
    <h1>🔌 WebSocket Chat Test</h1>
    
    <div class="container">
        <h3>Connection Status</h3>
        <div id="status">Disconnected</div>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>
    
    <div class="container">
        <h3>Player Setup</h3>
        <input type="text" id="nickname" placeholder="Enter nickname" value="TestPlayer">
        <button onclick="setNickname()">Set Nickname</button>
        <button onclick="createRoom()">Create Room</button>
        <button onclick="joinRoom()">Join Room</button>
    </div>
    
    <div class="container">
        <h3>Chat</h3>
        <div id="messages"></div>
        <input type="text" id="messageInput" placeholder="Type a message..." onkeypress="if(event.key==='Enter') sendMessage()">
        <button onclick="sendMessage()">Send Message</button>
    </div>
    
    <div class="container">
        <h3>Debug Log</h3>
        <div id="debug" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        <button onclick="clearDebug()">Clear Debug</button>
    </div>

    <script>
        let ws = null;
        let playerId = null;
        let roomCode = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const debugDiv = document.getElementById('debug');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#e74c3c' : type === 'success' ? '#2ecc71' : '#bdc3c7';
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugDiv.appendChild(logEntry);
            debugDiv.scrollTop = debugDiv.scrollHeight;
            console.log(message);
        }
        
        function addMessage(content, type = 'message') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }
        
        function connect() {
            if (ws) {
                log('Already connected');
                return;
            }
            
            try {
                log('Connecting to WebSocket server...');
                ws = new WebSocket('ws://localhost:8082');
                
                ws.onopen = function(event) {
                    log('WebSocket connected successfully', 'success');
                    updateStatus('Connected');
                    addMessage('<span class="success">Connected to server!</span>', 'system');
                };
                
                ws.onmessage = function(event) {
                    log(`Received message: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (error) {
                        log(`Error parsing message: ${error.message}`, 'error');
                    }
                };
                
                ws.onclose = function(event) {
                    log(`WebSocket closed: ${event.code} ${event.reason}`);
                    updateStatus('Disconnected');
                    addMessage('<span class="error">Disconnected from server</span>', 'system');
                    ws = null;
                };
                
                ws.onerror = function(error) {
                    log(`WebSocket error: ${error}`, 'error');
                    addMessage('<span class="error">Connection error</span>', 'system');
                };
                
            } catch (error) {
                log(`Failed to create WebSocket: ${error.message}`, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                updateStatus('Disconnected');
                log('Disconnected from WebSocket');
            }
        }
        
        function sendWebSocketMessage(type, data = {}) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket not connected', 'error');
                return false;
            }
            
            const message = {
                type: type,
                data: data,
                timestamp: Date.now()
            };
            
            log(`Sending: ${JSON.stringify(message)}`);
            ws.send(JSON.stringify(message));
            return true;
        }
        
        function handleMessage(message) {
            const { type, data } = message;
            
            switch (type) {
                case 'chatMessage':
                    addMessage(`<strong>${data.nickname}:</strong> ${data.message}`);
                    break;
                case 'playerJoined':
                    addMessage(`<span class="success">${data.nickname} joined the game!</span>`, 'system');
                    break;
                case 'playerLeft':
                    addMessage(`<span class="error">A player left the game</span>`, 'system');
                    break;
                case 'roomCreated':
                    roomCode = data.roomCode;
                    addMessage(`<span class="success">Room created: ${roomCode}</span>`, 'system');
                    break;
                case 'roomJoined':
                    addMessage(`<span class="success">Joined room successfully</span>`, 'system');
                    break;
                case 'error':
                    addMessage(`<span class="error">Error: ${data.message}</span>`, 'system');
                    break;
                default:
                    log(`Unknown message type: ${type}`);
            }
        }
        
        function setNickname() {
            const nickname = document.getElementById('nickname').value.trim();
            if (!nickname) {
                log('Please enter a nickname', 'error');
                return;
            }
            
            if (sendWebSocketMessage('setNickname', { nickname: nickname })) {
                log(`Setting nickname to: ${nickname}`);
            }
        }
        
        function createRoom() {
            if (sendWebSocketMessage('createRoom', {})) {
                log('Creating room...');
            }
        }
        
        function joinRoom() {
            const code = roomCode || 'SIMPLE';
            if (sendWebSocketMessage('joinRoom', { roomCode: code })) {
                log(`Joining room: ${code}`);
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            const nickname = document.getElementById('nickname').value.trim();
            
            if (!message) return;
            if (!nickname) {
                log('Please set a nickname first', 'error');
                return;
            }
            
            if (sendWebSocketMessage('chatMessage', {
                nickname: nickname,
                message: message,
                timestamp: Date.now()
            })) {
                input.value = '';
                log(`Sent chat message: ${message}`);
            }
        }
        
        function clearDebug() {
            document.getElementById('debug').innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded, ready to test WebSocket connection');
        };
    </script>
</body>
</html>
