/**
 * Multiplayer WebSocket System for Bomberman
 */

class MultiplayerManager {
    constructor(game) {
        this.game = game;
        this.socket = null;
        this.isConnected = false;
        this.playerId = null;
        this.playerNickname = '';
        this.roomCode = null;
        this.isHost = false;
        this.players = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        // Message queue for when disconnected
        this.messageQueue = [];
        
        // Chat system
        this.chatMessages = [];
        this.maxChatMessages = 50;
        
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        // Bind methods to preserve context
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }
    
    connect(serverUrl = 'ws://localhost:8081') {
        try {
            this.socket = new WebSocket(serverUrl);
            this.socket.addEventListener('open', this.onOpen);
            this.socket.addEventListener('message', this.onMessage);
            this.socket.addEventListener('close', this.onClose);
            this.socket.addEventListener('error', this.onError);
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.handleConnectionError();
        }
    }
    
    disconnect() {
        if (this.socket) {
            this.socket.removeEventListener('open', this.onOpen);
            this.socket.removeEventListener('message', this.onMessage);
            this.socket.removeEventListener('close', this.onClose);
            this.socket.removeEventListener('error', this.onError);
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
    }
    
    onOpen(event) {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Send any queued messages
        this.flushMessageQueue();
        
        // Notify game of connection
        this.game.framework.emit('connectionEstablished');
    }
    
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    onClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        
        // Attempt to reconnect if not intentional disconnect
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
        
        // Notify game of disconnection
        this.game.framework.emit('connectionLost');
    }
    
    onError(event) {
        console.error('WebSocket error:', event);
        this.handleConnectionError();
    }
    
    attemptReconnect() {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay * this.reconnectAttempts);
    }
    
    handleConnectionError() {
        // Fallback to local mode or show error
        console.warn('Multiplayer connection failed, falling back to local mode');
        this.game.framework.emit('connectionFailed');
    }
    
    sendMessage(type, data = {}) {
        const message = {
            type,
            data,
            timestamp: Date.now(),
            playerId: this.playerId
        };
        
        if (this.isConnected && this.socket) {
            try {
                this.socket.send(JSON.stringify(message));
            } catch (error) {
                console.error('Failed to send message:', error);
                this.queueMessage(message);
            }
        } else {
            this.queueMessage(message);
        }
    }
    
    queueMessage(message) {
        this.messageQueue.push(message);
        
        // Limit queue size
        if (this.messageQueue.length > 100) {
            this.messageQueue.shift();
        }
    }
    
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            try {
                this.socket.send(JSON.stringify(message));
            } catch (error) {
                console.error('Failed to send queued message:', error);
                break;
            }
        }
    }
    
    handleMessage(message) {
        const { type, data, playerId, timestamp } = message;
        
        switch (type) {
            case 'playerJoined':
                this.handlePlayerJoined(data);
                break;
            case 'playerLeft':
                this.handlePlayerLeft(data);
                break;
            case 'roomCreated':
                this.handleRoomCreated(data);
                break;
            case 'roomJoined':
                this.handleRoomJoined(data);
                break;
            case 'gameStart':
                this.handleGameStart(data);
                break;
            case 'gameEnd':
                this.handleGameEnd(data);
                break;
            case 'playerMove':
                this.handlePlayerMove(data);
                break;
            case 'bombPlaced':
                this.handleBombPlaced(data);
                break;
            case 'bombExploded':
                this.handleBombExploded(data);
                break;
            case 'playerDied':
                this.handlePlayerDied(data);
                break;
            case 'powerUpCollected':
                this.handlePowerUpCollected(data);
                break;
            case 'chatMessage':
                this.handleChatMessage(data);
                break;
            case 'gameState':
                this.handleGameState(data);
                break;
            case 'error':
                this.handleError(data);
                break;
            default:
                console.warn('Unknown message type:', type);
        }
    }
    
    // Message handlers
    handlePlayerJoined(data) {
        this.players.set(data.playerId, data);
        this.game.framework.emit('playerJoined', data);
    }
    
    handlePlayerLeft(data) {
        this.players.delete(data.playerId);
        this.game.framework.emit('playerLeft', data);
    }
    
    handleRoomCreated(data) {
        this.roomCode = data.roomCode;
        this.isHost = true;
        this.game.framework.emit('roomCreated', data);
    }
    
    handleRoomJoined(data) {
        this.roomCode = data.roomCode;
        this.players = new Map(data.players.map(p => [p.playerId, p]));
        this.game.framework.emit('roomJoined', data);
    }
    
    handleGameStart(data) {
        this.game.framework.emit('gameStart', data);
    }
    
    handleGameEnd(data) {
        this.game.framework.emit('gameEnd', data);
    }
    
    handlePlayerMove(data) {
        this.game.framework.emit('playerMove', data);
    }
    
    handleBombPlaced(data) {
        this.game.framework.emit('bombPlaced', data);
    }
    
    handleBombExploded(data) {
        this.game.framework.emit('bombExploded', data);
    }
    
    handlePlayerDied(data) {
        this.game.framework.emit('playerDied', data);
    }
    
    handlePowerUpCollected(data) {
        this.game.framework.emit('powerUpCollected', data);
    }
    
    handleChatMessage(data) {
        this.addChatMessage(data);
        this.game.framework.emit('chatMessage', data);
    }
    
    handleGameState(data) {
        this.game.framework.emit('gameState', data);
    }
    
    handleError(data) {
        console.error('Server error:', data.message);
        this.game.framework.emit('serverError', data);
    }
    
    // Public API methods
    setNickname(nickname) {
        this.playerNickname = nickname;
        this.sendMessage('setNickname', { nickname });
    }
    
    createRoom() {
        this.sendMessage('createRoom');
    }
    
    joinRoom(roomCode) {
        this.sendMessage('joinRoom', { roomCode });
    }
    
    leaveRoom() {
        this.sendMessage('leaveRoom');
        this.roomCode = null;
        this.isHost = false;
        this.players.clear();
    }
    
    startGame() {
        if (this.isHost) {
            this.sendMessage('startGame');
        }
    }
    
    sendPlayerMove(x, y, direction) {
        this.sendMessage('playerMove', { x, y, direction });
    }
    
    sendBombPlace(x, y) {
        this.sendMessage('bombPlace', { x, y });
    }
    
    sendChatMessage(message) {
        const chatData = {
            message,
            nickname: this.playerNickname,
            timestamp: Date.now()
        };
        this.sendMessage('chatMessage', chatData);
    }
    
    // Chat system
    addChatMessage(data) {
        this.chatMessages.push(data);
        
        // Limit chat history
        if (this.chatMessages.length > this.maxChatMessages) {
            this.chatMessages.shift();
        }
    }
    
    getChatMessages() {
        return this.chatMessages;
    }
    
    clearChat() {
        this.chatMessages = [];
    }
    
    // Getters
    getPlayers() {
        return Array.from(this.players.values());
    }
    
    getPlayerCount() {
        return this.players.size;
    }
    
    getRoomCode() {
        return this.roomCode;
    }
    
    isConnectedToServer() {
        return this.isConnected;
    }
    
    isRoomHost() {
        return this.isHost;
    }
}

// Export for use
window.MultiplayerManager = MultiplayerManager;
