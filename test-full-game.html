<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Full Game Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #34495e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .warning { background: #f39c12; }
        .info { background: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #7f8c8d; cursor: not-allowed; }
        #game-container {
            width: 100%;
            height: 400px;
            border: 2px solid #3498db;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 Bomberman Full Game Test</h1>
        
        <div class="test-section">
            <h2>Framework Loading Test</h2>
            <div id="framework-status" class="status info">Testing framework loading...</div>
            <div id="framework-details"></div>
        </div>
        
        <div class="test-section">
            <h2>Game Initialization Test</h2>
            <div id="game-status" class="status info">Waiting for framework...</div>
            <button id="init-game-btn" onclick="testGameInit()" disabled>Initialize Game</button>
            <button id="start-game-btn" onclick="testGameStart()" disabled>Start Game Scene</button>
        </div>
        
        <div class="test-section">
            <h2>Multiplayer Connection Test</h2>
            <div id="multiplayer-status" class="status info">Not tested</div>
            <button id="test-multiplayer-btn" onclick="testMultiplayer()" disabled>Test WebSocket Connection</button>
        </div>
        
        <div class="test-section">
            <h2>Game Container</h2>
            <div id="game-container"></div>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <!-- Load framework scripts -->
    <script src="./static/js/framework/utils.js"></script>
    <script src="./static/js/framework/components.js"></script>
    <script src="./static/js/framework/systems.js"></script>
    <script src="./static/js/framework/core.js"></script>
    <script src="./static/js/game/multiplayer.js"></script>
    <script src="./static/js/game/map.js"></script>
    <script src="./static/js/game/player.js"></script>
    <script src="./static/js/game/bomb.js"></script>
    <script src="./static/js/game/scenes.js"></script>
    <script src="./static/js/game/gameScene.js"></script>
    <script src="./static/js/game/game.js"></script>

    <script>
        let gameInstance = null;
        let testResults = [];
        
        function addTestResult(test, status, message) {
            testResults.push({ test, status, message, timestamp: new Date() });
            updateTestResults();
        }
        
        function updateTestResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                    <small>(${result.timestamp.toLocaleTimeString()})</small>
                </div>`
            ).join('');
        }
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }
        
        function testFramework() {
            const checks = {
                'Utils': typeof window.Utils,
                'Systems': typeof window.Systems,
                'GameFramework': typeof window.GameFramework,
                'BombermanGame': typeof window.BombermanGame,
                'GameMap': typeof window.GameMap,
                'Player': typeof window.Player,
                'Bomb': typeof window.Bomb,
                'MultiplayerManager': typeof window.MultiplayerManager
            };
            
            let allLoaded = true;
            let details = '<ul>';
            
            for (const [name, type] of Object.entries(checks)) {
                const loaded = type !== 'undefined';
                if (!loaded) allLoaded = false;
                
                details += `<li style="color: ${loaded ? '#27ae60' : '#e74c3c'}">
                    ${name}: ${type} ${loaded ? '✓' : '✗'}
                </li>`;
            }
            
            details += '</ul>';
            
            if (allLoaded) {
                updateStatus('framework-status', 'success', '✓ All framework components loaded successfully!');
                document.getElementById('init-game-btn').disabled = false;
                addTestResult('Framework Loading', 'success', 'All components loaded');
            } else {
                updateStatus('framework-status', 'error', '✗ Some framework components failed to load');
                addTestResult('Framework Loading', 'error', 'Missing components');
            }
            
            document.getElementById('framework-details').innerHTML = details;
            return allLoaded;
        }
        
        function testGameInit() {
            try {
                updateStatus('game-status', 'info', 'Initializing game...');
                
                // Clear any existing game container content
                const container = document.getElementById('game-container');
                container.innerHTML = '';
                
                // Initialize the game
                gameInstance = new BombermanGame();
                
                updateStatus('game-status', 'success', '✓ Game initialized successfully!');
                document.getElementById('start-game-btn').disabled = false;
                document.getElementById('test-multiplayer-btn').disabled = false;
                addTestResult('Game Initialization', 'success', 'BombermanGame created');
                
            } catch (error) {
                console.error('Game initialization failed:', error);
                updateStatus('game-status', 'error', `✗ Game initialization failed: ${error.message}`);
                addTestResult('Game Initialization', 'error', error.message);
            }
        }
        
        function testGameStart() {
            if (!gameInstance) {
                updateStatus('game-status', 'error', 'Game not initialized');
                return;
            }
            
            try {
                // Set a test nickname
                gameInstance.playerNickname = 'TestPlayer';
                
                // Switch to game scene
                gameInstance.framework.switchScene('game');
                
                updateStatus('game-status', 'success', '✓ Game scene started!');
                addTestResult('Game Scene', 'success', 'Game scene activated');
                
            } catch (error) {
                console.error('Game start failed:', error);
                updateStatus('game-status', 'error', `✗ Game start failed: ${error.message}`);
                addTestResult('Game Scene', 'error', error.message);
            }
        }
        
        function testMultiplayer() {
            if (!gameInstance || !gameInstance.multiplayerManager) {
                updateStatus('multiplayer-status', 'error', 'Game or multiplayer manager not available');
                return;
            }
            
            try {
                const mp = gameInstance.multiplayerManager;
                
                if (mp.isConnected) {
                    updateStatus('multiplayer-status', 'success', '✓ WebSocket connected!');
                    addTestResult('Multiplayer', 'success', 'WebSocket connection active');
                } else {
                    updateStatus('multiplayer-status', 'warning', '⚠ WebSocket not connected (trying...)');
                    addTestResult('Multiplayer', 'warning', 'WebSocket connection pending');
                }
                
            } catch (error) {
                console.error('Multiplayer test failed:', error);
                updateStatus('multiplayer-status', 'error', `✗ Multiplayer test failed: ${error.message}`);
                addTestResult('Multiplayer', 'error', error.message);
            }
        }
        
        // Start testing after a delay
        setTimeout(() => {
            if (testFramework()) {
                setTimeout(testGameInit, 1000);
            }
        }, 2000);
    </script>
</body>
</html>
