<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Bomberman Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .game-container {
            text-align: center;
        }
        
        .game-board {
            display: inline-grid;
            grid-template-columns: repeat(15, 40px);
            grid-template-rows: repeat(13, 40px);
            gap: 1px;
            background: #333;
            padding: 10px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .cell {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            position: relative;
        }
        
        .empty { background: #2c3e50; }
        .wall { background: #34495e; }
        .block { background: #8b4513; }
        .player { background: #3498db; border-radius: 50%; }
        .bomb { background: #e74c3c; border-radius: 50%; animation: pulse 0.5s infinite; }
        .powerup { background: #f39c12; border-radius: 5px; animation: glow 1s infinite; }
        .explosion { background: #ff6b35; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px #f39c12; }
            50% { box-shadow: 0 0 15px #f39c12; }
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #2c3e50;
            border-radius: 10px;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 10px 0;
        }
        
        .stat {
            padding: 5px 10px;
            background: #34495e;
            border-radius: 5px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .test-button {
            background: #e74c3c;
        }
        
        .test-button:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎮 Simple Bomberman</h1>
        
        <div class="stats">
            <div class="stat">💣 Bombs: <span id="bomb-count">1</span></div>
            <div class="stat">🔥 Range: <span id="bomb-range">1</span></div>
            <div class="stat">⚡ Speed: <span id="speed">1.0</span>x</div>
            <div class="stat">❤️ Lives: <span id="lives">3</span></div>
        </div>
        
        <div class="game-board" id="game-board"></div>
        
        <div class="controls">
            <div>
                <strong>Controls:</strong><br>
                Arrow Keys: Move | Spacebar: Place Bomb
            </div>
            <div style="margin-top: 10px;">
                <button onclick="startGame()">🎮 Start Game</button>
                <button onclick="resetGame()">🔄 Reset</button>
                <button class="test-button" onclick="testPowerUps()">💣 Test Power-ups</button>
                <button class="test-button" onclick="testBombs()">💥 Test Bombs</button>
            </div>
        </div>
        
        <div id="message" style="margin-top: 20px; font-size: 18px; color: #f39c12;"></div>
    </div>

    <script>
        // Game state
        let gameBoard = [];
        let player = { x: 1, y: 1 };
        let bombs = [];
        let powerUps = [];
        let explosions = [];
        let gameRunning = false;
        
        // Player stats
        let playerStats = {
            maxBombs: 1,
            bombRange: 1,
            speed: 1.0,
            lives: 3,
            activeBombs: 0
        };
        
        // Game config
        const BOARD_WIDTH = 15;
        const BOARD_HEIGHT = 13;
        const CELL_TYPES = {
            EMPTY: 0,
            WALL: 1,
            BLOCK: 2
        };
        
        // Initialize game
        function initGame() {
            createBoard();
            renderBoard();
            updateStats();
            showMessage("Game initialized! Press Start Game to begin.");
        }
        
        function createBoard() {
            gameBoard = [];
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                gameBoard[y] = [];
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (x === 0 || x === BOARD_WIDTH - 1 || y === 0 || y === BOARD_HEIGHT - 1) {
                        gameBoard[y][x] = CELL_TYPES.WALL;
                    } else if (x % 2 === 0 && y % 2 === 0) {
                        gameBoard[y][x] = CELL_TYPES.WALL;
                    } else if ((x === 1 && y === 1) || (x === 2 && y === 1) || (x === 1 && y === 2)) {
                        gameBoard[y][x] = CELL_TYPES.EMPTY; // Player spawn area
                    } else if (Math.random() < 0.3) {
                        gameBoard[y][x] = CELL_TYPES.BLOCK;
                    } else {
                        gameBoard[y][x] = CELL_TYPES.EMPTY;
                    }
                }
            }
        }
        
        function renderBoard() {
            const boardElement = document.getElementById('game-board');
            boardElement.innerHTML = '';
            
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;
                    
                    // Set base cell type
                    if (gameBoard[y][x] === CELL_TYPES.WALL) {
                        cell.classList.add('wall');
                        cell.textContent = '🧱';
                    } else if (gameBoard[y][x] === CELL_TYPES.BLOCK) {
                        cell.classList.add('block');
                        cell.textContent = '📦';
                    } else {
                        cell.classList.add('empty');
                    }
                    
                    // Add player
                    if (x === player.x && y === player.y) {
                        cell.classList.add('player');
                        cell.textContent = '😊';
                    }
                    
                    // Add bombs
                    bombs.forEach(bomb => {
                        if (bomb.x === x && bomb.y === y) {
                            cell.classList.add('bomb');
                            cell.textContent = '💣';
                        }
                    });
                    
                    // Add power-ups
                    powerUps.forEach(powerUp => {
                        if (powerUp.x === x && powerUp.y === y) {
                            cell.classList.add('powerup');
                            cell.textContent = powerUp.symbol;
                        }
                    });
                    
                    // Add explosions
                    explosions.forEach(explosion => {
                        if (explosion.x === x && explosion.y === y) {
                            cell.classList.add('explosion');
                            cell.textContent = '💥';
                        }
                    });
                    
                    boardElement.appendChild(cell);
                }
            }
        }
        
        function updateStats() {
            document.getElementById('bomb-count').textContent = playerStats.maxBombs;
            document.getElementById('bomb-range').textContent = playerStats.bombRange;
            document.getElementById('speed').textContent = playerStats.speed.toFixed(1);
            document.getElementById('lives').textContent = playerStats.lives;
        }
        
        function showMessage(text) {
            document.getElementById('message').textContent = text;
        }
        
        function startGame() {
            gameRunning = true;
            showMessage("Game started! Use arrow keys to move, spacebar to place bombs!");
        }
        
        function resetGame() {
            gameRunning = false;
            player = { x: 1, y: 1 };
            bombs = [];
            powerUps = [];
            explosions = [];
            playerStats = { maxBombs: 1, bombRange: 1, speed: 1.0, lives: 3, activeBombs: 0 };
            initGame();
        }
        
        // Test functions
        function testPowerUps() {
            powerUps = [
                { x: 3, y: 3, type: 'bombs', symbol: '💣' },
                { x: 5, y: 5, type: 'flames', symbol: '🔥' },
                { x: 7, y: 7, type: 'speed', symbol: '⚡' }
            ];
            renderBoard();
            showMessage("Test power-ups spawned! Walk over them to collect.");
        }
        
        function testBombs() {
            if (playerStats.activeBombs < playerStats.maxBombs) {
                placeBomb(player.x, player.y);
                showMessage("Test bomb placed! It will explode in 3 seconds.");
            }
        }
        
        // Movement and controls
        function movePlayer(dx, dy) {
            if (!gameRunning) return;

            const newX = player.x + dx;
            const newY = player.y + dy;

            if (canMoveTo(newX, newY)) {
                player.x = newX;
                player.y = newY;

                // Check for power-up collection
                checkPowerUpCollection();
                renderBoard();
            }
        }

        function canMoveTo(x, y) {
            if (x < 0 || x >= BOARD_WIDTH || y < 0 || y >= BOARD_HEIGHT) return false;
            if (gameBoard[y][x] === CELL_TYPES.WALL || gameBoard[y][x] === CELL_TYPES.BLOCK) return false;

            // Check for bombs (can't walk through them)
            return !bombs.some(bomb => bomb.x === x && bomb.y === y);
        }

        function checkPowerUpCollection() {
            powerUps = powerUps.filter(powerUp => {
                if (powerUp.x === player.x && powerUp.y === player.y) {
                    collectPowerUp(powerUp);
                    return false; // Remove collected power-up
                }
                return true;
            });
        }

        function collectPowerUp(powerUp) {
            switch (powerUp.type) {
                case 'bombs':
                    playerStats.maxBombs++;
                    showMessage(`💣 Collected bomb power-up! Max bombs: ${playerStats.maxBombs}`);
                    break;
                case 'flames':
                    playerStats.bombRange++;
                    showMessage(`🔥 Collected flame power-up! Bomb range: ${playerStats.bombRange}`);
                    break;
                case 'speed':
                    playerStats.speed += 0.2;
                    showMessage(`⚡ Collected speed power-up! Speed: ${playerStats.speed.toFixed(1)}x`);
                    break;
            }
            updateStats();
        }

        function placeBomb(x, y) {
            if (playerStats.activeBombs >= playerStats.maxBombs) return;
            if (bombs.some(bomb => bomb.x === x && bomb.y === y)) return;

            const bomb = { x, y, timer: 3 };
            bombs.push(bomb);
            playerStats.activeBombs++;

            // Explode after 3 seconds
            setTimeout(() => explodeBomb(bomb), 3000);
            renderBoard();
        }

        function explodeBomb(bomb) {
            // Remove bomb
            bombs = bombs.filter(b => b !== bomb);
            playerStats.activeBombs--;

            // Create explosion
            const explosionCells = [{ x: bomb.x, y: bomb.y }];

            // Explosion in 4 directions
            for (let dir = 0; dir < 4; dir++) {
                const dx = [0, 1, 0, -1][dir];
                const dy = [-1, 0, 1, 0][dir];

                for (let i = 1; i <= playerStats.bombRange; i++) {
                    const x = bomb.x + dx * i;
                    const y = bomb.y + dy * i;

                    if (x < 0 || x >= BOARD_WIDTH || y < 0 || y >= BOARD_HEIGHT) break;
                    if (gameBoard[y][x] === CELL_TYPES.WALL) break;

                    explosionCells.push({ x, y });

                    // Destroy blocks and spawn power-ups
                    if (gameBoard[y][x] === CELL_TYPES.BLOCK) {
                        gameBoard[y][x] = CELL_TYPES.EMPTY;

                        // 30% chance to spawn power-up
                        if (Math.random() < 0.3) {
                            const types = ['bombs', 'flames', 'speed'];
                            const symbols = ['💣', '🔥', '⚡'];
                            const type = types[Math.floor(Math.random() * types.length)];
                            const symbol = symbols[types.indexOf(type)];

                            powerUps.push({ x, y, type, symbol });
                        }
                        break;
                    }
                }
            }

            // Show explosion
            explosions = explosionCells;
            renderBoard();

            // Clear explosion after 500ms
            setTimeout(() => {
                explosions = [];
                renderBoard();
            }, 500);

            showMessage(`💥 Bomb exploded! Range: ${playerStats.bombRange}`);
        }

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    movePlayer(0, -1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    movePlayer(0, 1);
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    movePlayer(-1, 0);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    movePlayer(1, 0);
                    break;
                case ' ':
                    e.preventDefault();
                    placeBomb(player.x, player.y);
                    break;
            }
        });

        // Initialize on load
        window.onload = initGame;
    </script>
</body>
</html>
