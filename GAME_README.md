# Bomberman Multiplayer Game

A multiplayer Bomberman game built with a custom DOM-based framework, featuring real-time multiplayer gameplay via WebSockets.

## Features

### Core Game Mechanics
- **Multiplayer Support**: 2-4 players in real-time
- **Classic Bomberman Gameplay**: Place bombs, destroy blocks, collect power-ups
- **Player Lives**: Each player starts with 3 lives
- **Power-ups**: 
  - 💣 Bombs: Increase max bombs by 1
  - 🔥 Flames: Increase explosion range by 1
  - ⚡ Speed: Increase movement speed

### Game Flow
1. **Nickname Entry**: Players enter their nickname
2. **Lobby System**: Wait for 2-4 players with real-time chat
3. **Auto-start Logic**: 
   - With 4 players: 10-second countdown starts immediately
   - With 2-3 players: 20-second wait, then 10-second countdown
4. **Game Play**: Battle until one player remains
5. **Game Over**: Show winner and game statistics

### Technical Features
- **Custom Framework**: Built with a lightweight DOM-based game framework
- **60 FPS Performance**: Optimized rendering with requestAnimationFrame
- **Real-time Multiplayer**: WebSocket-based synchronization
- **Performance Monitoring**: Press F1 to toggle performance display
- **Responsive Design**: Works on different screen sizes

## Installation and Setup

### Prerequisites
- Node.js (version 14 or higher)
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Installation
1. Clone or download the project files
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Game
1. Start the server:
   ```bash
   npm start
   ```
2. Open your browser and go to: `http://localhost:8080`
3. For multiplayer testing, open multiple browser tabs or use different browsers

## Game Controls

### Movement
- **Arrow Keys** or **WASD**: Move player
- **Spacebar**: Place bomb

### UI Controls
- **F1**: Toggle performance display
- **H**: Toggle controls help
- **Enter**: Send chat message (in lobby)

## Game Rules

### Objective
Be the last player standing by eliminating opponents with bomb explosions.

### Gameplay
1. **Movement**: Players can move freely around the map
2. **Bomb Placement**: Place bombs that explode after 3 seconds
3. **Explosions**: Bombs explode in a cross pattern, destroying blocks and damaging players
4. **Power-ups**: Destroy blocks to reveal random power-ups
5. **Lives**: Players lose a life when caught in explosions
6. **Invulnerability**: 2-second invulnerability period after taking damage
7. **Victory**: Last player alive wins the game

### Map Layout
- **Walls** (gray): Indestructible barriers
- **Blocks** (brown): Destructible obstacles that may contain power-ups
- **Spawn Areas** (blue): Safe starting positions in corners

## Architecture

### Custom Framework Components
- **Entity-Component System**: Flexible game object management
- **Rendering System**: Optimized DOM manipulation
- **Physics System**: Movement and collision detection
- **Input System**: Keyboard and mouse handling
- **Audio System**: Sound effects and music support
- **Performance System**: FPS monitoring and optimization

### Game Systems
- **Player Management**: Player entities with stats and abilities
- **Map System**: Procedural map generation with collision detection
- **Bomb System**: Bomb placement, timing, and explosion mechanics
- **Power-up System**: Random power-up generation and collection
- **Multiplayer System**: WebSocket-based real-time synchronization

## File Structure

```
bomberman/
├── game.html              # Main game page
├── server.js              # WebSocket server
├── package.json           # Node.js dependencies
├── static/
│   ├── style.css          # Game styles
│   ├── fonts/             # Custom fonts
│   ├── images/            # Game assets
│   └── js/
│       ├── framework/     # Custom game framework
│       │   ├── core.js    # Framework core
│       │   ├── components.js # ECS components
│       │   ├── systems.js # Game systems
│       │   └── utils.js   # Utility functions
│       └── game/          # Game implementation
│           ├── game.js    # Main game class
│           ├── scenes.js  # Menu and lobby scenes
│           ├── gameScene.js # Game and game over scenes
│           ├── multiplayer.js # WebSocket client
│           ├── map.js     # Map generation and management
│           ├── player.js  # Player entities
│           └── bomb.js    # Bomb and explosion system
```

## Performance Requirements

The game is designed to meet strict performance requirements:
- **60 FPS**: Consistent frame rate at all times
- **No Frame Drops**: Smooth gameplay without stuttering
- **Optimized Rendering**: Minimal DOM manipulation and repaints
- **Memory Efficient**: Proper cleanup and object pooling

## Multiplayer Features

### Real-time Synchronization
- Player movements
- Bomb placement and explosions
- Power-up collection
- Game state updates

### Chat System
- Real-time messaging in lobby
- Player join/leave notifications
- Game event announcements

### Room Management
- Automatic room creation
- Player capacity management (2-4 players)
- Graceful handling of disconnections

## Development Notes

### Framework Design
The custom framework follows modern game development patterns:
- Entity-Component-System architecture
- Separation of concerns between systems
- Performance-first design with minimal overhead
- Extensible component system for easy feature addition

### Performance Optimizations
- Transform caching to minimize DOM updates
- Efficient collision detection with spatial partitioning
- Object pooling for frequently created/destroyed objects
- Optimized rendering pipeline with batched updates

## Troubleshooting

### Common Issues
1. **Game won't load**: Check browser console for JavaScript errors
2. **Multiplayer not working**: Ensure WebSocket server is running
3. **Performance issues**: Press F1 to check FPS and frame times
4. **Controls not responding**: Make sure the game window has focus

### Browser Compatibility
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Future Enhancements

### Bonus Features (Planned)
- **AI Players**: Computer-controlled opponents
- **Team Mode**: 2v2 gameplay
- **Ghost Mode**: Interaction after death
- **Additional Power-ups**: Bomb push, block pass, detonator
- **Multiple Maps**: Different map layouts and themes
- **Sound Effects**: Audio feedback for game events

## License

This project is for educational purposes. Feel free to modify and extend the codebase.
