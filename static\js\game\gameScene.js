/**
 * Game Scene Implementation for Bomberman
 */

// Wait for BombermanGame to be defined, then extend it
(function() {
    function extendBombermanGame() {
        if (typeof BombermanGame === 'undefined') {
            setTimeout(extendBombermanGame, 100);
            return;
        }

        // Extend the BombermanGame class with game scene implementations
        Object.assign(BombermanGame.prototype, {
    
    // Game Scene Implementation
    enterGameScene() {
        this.currentState = 'GAME';
        this.gameStartTime = Date.now();
        this.clearGameContainer();
        
        // Initialize game objects
        this.players = new Map();
        this.bombs = new Map();
        this.powerUps = new Map();
        
        // Create game map
        this.gameMap = new GameMap(this);
        
        // Create players
        this.createPlayers();
        
        // Create game UI
        this.createGameUI();
        
        // Setup game event listeners
        this.setupGameEventListeners();
        
        console.log('Game started!');
    },
    
    exitGameScene() {
        // Cleanup game objects
        if (this.gameMap) {
            this.gameMap.destroy();
            this.gameMap = null;
        }
        
        // Cleanup players
        this.players.forEach(player => player.destroy());
        this.players.clear();
        
        // Cleanup bombs
        this.bombs.forEach(bomb => bomb.destroy());
        this.bombs.clear();
        
        // Cleanup power-ups
        this.powerUps.clear();
        
        // Remove game UI
        if (this.gameUI) {
            this.gameContainer.removeChild(this.gameUI);
            this.gameUI = null;
        }
        
        console.log('Exiting game scene');
    },
    
    updateGameScene(deltaTime) {
        // Update players
        this.players.forEach(player => {
            player.update(deltaTime);
        });
        
        // Check win condition
        this.checkWinCondition();
        
        // Update game UI
        this.updateGameUI();
    },
    
    renderGameScene() {
        // Game rendering is handled by DOM and framework systems
    },
    
    createPlayers() {
        // For now, create local player and AI/remote players
        const playerCount = this.multiplayerManager ? 
            Math.max(2, this.multiplayerManager.getPlayerCount()) : 2;
        
        for (let i = 0; i < Math.min(playerCount, 4); i++) {
            const playerId = `player-${i}`;
            const nickname = i === 0 ? this.playerNickname : `Player ${i + 1}`;
            
            const player = new Player(this, playerId, i, nickname);
            this.players.set(playerId, player);
        }
    },
    
    createGameUI() {
        this.gameUI = Utils.DOM.createElement('div', 'game-ui', {
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: '100',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Player stats
        this.playerStatsContainer = Utils.DOM.createElement('div', 'player-stats', {
            background: 'rgba(0,0,0,0.8)',
            padding: '15px',
            borderRadius: '10px',
            marginBottom: '10px',
            border: '2px solid #34495e'
        });
        
        // Game timer
        this.gameTimerContainer = Utils.DOM.createElement('div', 'game-timer', {
            background: 'rgba(0,0,0,0.8)',
            padding: '10px 15px',
            borderRadius: '10px',
            textAlign: 'center',
            border: '2px solid #34495e',
            fontSize: '18px',
            fontWeight: 'bold'
        });
        
        this.gameUI.appendChild(this.playerStatsContainer);
        this.gameUI.appendChild(this.gameTimerContainer);
        this.gameContainer.appendChild(this.gameUI);
        
        this.updatePlayerStats();
    },
    
    updateGameUI() {
        this.updatePlayerStats();
        this.updateGameTimer();
    },
    
    updatePlayerStats() {
        if (!this.playerStatsContainer) return;
        
        this.playerStatsContainer.innerHTML = '';
        
        // Get local player (first player)
        const localPlayer = this.players.get('player-0');
        if (!localPlayer) return;
        
        const stats = localPlayer.getStats();
        
        // Lives
        const livesItem = this.createStatItem('❤️', `Lives: ${stats.lives}`, '#e74c3c');
        this.playerStatsContainer.appendChild(livesItem);
        
        // Bombs
        const bombsItem = this.createStatItem('💣', `Bombs: ${stats.maxBombs}`, '#2c3e50');
        this.playerStatsContainer.appendChild(bombsItem);
        
        // Flames
        const flamesItem = this.createStatItem('🔥', `Range: ${stats.bombRange}`, '#f39c12');
        this.playerStatsContainer.appendChild(flamesItem);
        
        // Speed
        const speedItem = this.createStatItem('⚡', `Speed: ${stats.speedMultiplier.toFixed(1)}x`, '#2ecc71');
        this.playerStatsContainer.appendChild(speedItem);
    },
    
    createStatItem(icon, text, color) {
        const item = Utils.DOM.createElement('div', 'stat-item', {
            display: 'flex',
            alignItems: 'center',
            marginBottom: '8px',
            fontSize: '14px'
        });
        
        const iconElement = Utils.DOM.createElement('div', 'stat-icon', {
            width: '20px',
            height: '20px',
            marginRight: '10px',
            borderRadius: '3px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            backgroundColor: color
        });
        iconElement.textContent = icon;
        
        const textElement = Utils.DOM.createElement('span');
        textElement.textContent = text;
        
        item.appendChild(iconElement);
        item.appendChild(textElement);
        
        return item;
    },
    
    updateGameTimer() {
        if (!this.gameTimerContainer) return;
        
        const elapsed = Math.floor((Date.now() - this.gameStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        this.gameTimerContainer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    },
    
    setupGameEventListeners() {
        // Listen for game events
        this.framework.on('playerDied', (data) => {
            console.log('Player died:', data);
            this.checkWinCondition();
        });
        
        this.framework.on('bombPlaced', (data) => {
            console.log('Bomb placed:', data);
        });
        
        this.framework.on('bombExploded', (data) => {
            console.log('Bomb exploded:', data);
        });
        
        this.framework.on('powerUpCollected', (data) => {
            console.log('Power-up collected:', data);
            this.updatePlayerStats();
        });
    },
    
    checkWinCondition() {
        const alivePlayers = Array.from(this.players.values()).filter(p => p.isAlive);
        
        if (alivePlayers.length <= 1) {
            // Game over
            const winner = alivePlayers.length === 1 ? alivePlayers[0] : null;
            this.endGame(winner);
        }
    },
    
    endGame(winner) {
        this.gameWinner = winner;
        this.framework.switchScene('gameOver');
    },
    
    // Game Over Scene Implementation
    enterGameOverScene() {
        this.currentState = 'GAME_OVER';
        this.gameEndTime = Date.now();
        this.clearGameContainer();
        
        this.gameOverContainer = Utils.DOM.createElement('div', 'game-over-scene', {
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#07131F',
            color: 'white',
            fontFamily: 'EngraversGothic, sans-serif'
        });
        
        // Game Over Title
        const title = Utils.DOM.createElement('h1', 'game-over-title', {
            fontSize: '4rem',
            marginBottom: '30px',
            textTransform: 'uppercase',
            letterSpacing: '10px',
            color: '#e74c3c',
            textShadow: '3px 3px 6px rgba(0,0,0,0.8)'
        });
        title.textContent = 'Game Over';
        
        // Winner announcement
        const winnerText = Utils.DOM.createElement('h2', 'winner-text', {
            fontSize: '2rem',
            marginBottom: '40px',
            color: '#f39c12'
        });
        
        if (this.gameWinner) {
            winnerText.textContent = `${this.gameWinner.nickname} Wins!`;
        } else {
            winnerText.textContent = 'Draw!';
        }
        
        // Game stats
        const statsContainer = Utils.DOM.createElement('div', 'game-stats', {
            backgroundColor: '#2c3e50',
            padding: '30px',
            borderRadius: '15px',
            marginBottom: '40px',
            textAlign: 'center'
        });
        
        const gameDuration = Math.floor((this.gameEndTime - this.gameStartTime) / 1000);
        const minutes = Math.floor(gameDuration / 60);
        const seconds = gameDuration % 60;
        
        const durationText = Utils.DOM.createElement('p', '', {
            fontSize: '1.2rem',
            marginBottom: '10px'
        });
        durationText.textContent = `Game Duration: ${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const playersText = Utils.DOM.createElement('p', '', {
            fontSize: '1.2rem'
        });
        playersText.textContent = `Players: ${this.players.size}`;
        
        statsContainer.appendChild(durationText);
        statsContainer.appendChild(playersText);
        
        // Buttons
        const buttonsContainer = Utils.DOM.createElement('div', 'game-over-buttons', {
            display: 'flex',
            gap: '20px'
        });
        
        const playAgainButton = this.createMenuButton('Play Again', () => {
            this.framework.switchScene('lobby');
        });
        
        const mainMenuButton = this.createMenuButton('Main Menu', () => {
            this.framework.switchScene('menu');
        }, '#e74c3c');
        
        buttonsContainer.appendChild(playAgainButton);
        buttonsContainer.appendChild(mainMenuButton);
        
        this.gameOverContainer.appendChild(title);
        this.gameOverContainer.appendChild(winnerText);
        this.gameOverContainer.appendChild(statsContainer);
        this.gameOverContainer.appendChild(buttonsContainer);
        
        this.gameContainer.appendChild(this.gameOverContainer);
        
        console.log('Game over scene entered');
    },
    
    exitGameOverScene() {
        if (this.gameOverContainer) {
            this.gameContainer.removeChild(this.gameOverContainer);
            this.gameOverContainer = null;
        }
        
        // Reset game state
        this.gameWinner = null;
        this.gameStartTime = 0;
        this.gameEndTime = 0;
    },
    
    updateGameOverScene(deltaTime) {
        // Game over scene update logic
    },
    
    renderGameOverScene() {
        // Game over rendering is handled by DOM
    }
        }); // End of Object.assign
    } // End of extendBombermanGame function

    // Start the extension process
    extendBombermanGame();
})(); // End of IIFE
